/* @import "tailwindcss"; */
/* @import url("https://fonts.googleapis.com/css2?family=Urbanist:wght@100;200;300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap"); */
@import url('https://fonts.googleapis.com/css2?family=Fredoka:wght@300..700&family=Lora:ital,wght@0,400..700;1,400..700&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Nunito:ital,wght@0,200..1000;1,200..1000&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Space+Grotesk:wght@300..700&family=Urbanist:ital,wght@0,100;1,100&display=swap');
@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme {
  --transition-duration-200: 200ms;

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive)); /* Note: You had destructive, I'm adding danger as specifically requested */
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  /* Added Success and Danger colors */
  --color-success: hsl(var(--success));
  --color-success-foreground: hsl(var(--success-foreground));
  --color-success-light: hsl(var(--success-light));
  --color-danger: hsl(var(--danger));
  --color-danger-foreground: hsl(var(--danger-foreground));
  --color-danger-light: hsl(var(--danger-light));
  --color-accent-light: hsl(var(--accent-light));

  /* Chart and visualization colors */
  --color-chart-primary: hsl(var(--chart-primary));
  --color-chart-secondary: hsl(var(--chart-secondary));
  --color-chart-tertiary: hsl(var(--chart-tertiary));
  --color-chart-quaternary: hsl(var(--chart-quaternary));

  /* Status and feedback colors */
  --color-info: hsl(var(--info));
  --color-info-foreground: hsl(var(--info-foreground));
  --color-warning: hsl(var(--warning));
  --color-warning-foreground: hsl(var(--warning-foreground));

  /* Subject-specific colors */
  --color-subject-math: hsl(var(--subject-math));
  --color-subject-math-foreground: hsl(var(--subject-math-foreground));
  --color-subject-science: hsl(var(--subject-science));
  --color-subject-science-foreground: hsl(var(--subject-science-foreground));
  --color-subject-english: hsl(var(--subject-english));
  --color-subject-english-foreground: hsl(var(--subject-english-foreground));
  --color-subject-history: hsl(var(--subject-history));
  --color-subject-history-foreground: hsl(var(--subject-history-foreground));
  --color-subject-computer: hsl(var(--subject-computer));
  --color-subject-computer-foreground: hsl(var(--subject-computer-foreground));
  --color-subject-upsc: hsl(var(--subject-upsc));
  --color-subject-upsc-foreground: hsl(var(--subject-upsc-foreground));

  /* Proficiency level colors */
  --color-proficiency-excellent: hsl(var(--proficiency-excellent));
  --color-proficiency-excellent-foreground: hsl(var(--proficiency-excellent-foreground));
  --color-proficiency-good: hsl(var(--proficiency-good));
  --color-proficiency-good-foreground: hsl(var(--proficiency-good-foreground));
  --color-proficiency-average: hsl(var(--proficiency-average));
  --color-proficiency-average-foreground: hsl(var(--proficiency-average-foreground));
  --color-proficiency-needs-improvement: hsl(var(--proficiency-needs-improvement));
  --color-proficiency-needs-improvement-foreground: hsl(var(--proficiency-needs-improvement-foreground));

  /* Graph and network colors */
  --color-graph-subject: hsl(var(--graph-subject));
  --color-graph-chapter: hsl(var(--graph-chapter));
  --color-graph-subtopic: hsl(var(--graph-subtopic));
  --color-graph-edge-primary: hsl(var(--graph-edge-primary));
  --color-graph-edge-secondary: hsl(var(--graph-edge-secondary));
  --color-graph-edge-tertiary: hsl(var(--graph-edge-tertiary));

  /* Node-specific colors for graph visualization */
  --color-node-subject: hsl(var(--graph-subject));
  --color-node-subject-foreground: hsl(var(--background));
  --color-node-subject-light: hsl(var(--graph-subject) / 0.1);
  --color-node-subject-border: hsl(var(--graph-subject) / 0.3);
  --color-node-chapter: hsl(var(--graph-chapter));
  --color-node-chapter-foreground: hsl(var(--background));
  --color-node-chapter-light: hsl(var(--graph-chapter) / 0.1);
  --color-node-chapter-border: hsl(var(--graph-chapter) / 0.3);
  --color-node-subtopic: hsl(var(--graph-subtopic));
  --color-node-subtopic-foreground: hsl(var(--background));
  --color-node-subtopic-light: hsl(var(--graph-subtopic) / 0.1);
  --color-node-subtopic-border: hsl(var(--graph-subtopic) / 0.3);

  /* Additional utility colors */
  --color-neutral: hsl(var(--muted));
  --color-neutral-foreground: hsl(var(--muted-foreground));
  --color-neutral-light: hsl(var(--muted) / 0.5);
  --color-neutral-border: hsl(var(--border));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  --font-sans: "Urbanist", "Space Grotesk", system-ui, sans-serif;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  width: 100%;
  @media (width >= 640px) {
    max-width: 640px;
  }
  @media (width >= 768px) {
    max-width: 768px;
  }
  @media (width >= 1024px) {
    max-width: 1024px;
  }
  @media (width >= 1280px) {
    max-width: 1280px;
  }
  @media (width >= 1536px) {
    max-width: 1536px;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: hsl(var(--border));
  }
}

/* Update these values in your existing theme */

@layer base {
  :root {
    --card: 220 30% 99%; /* Almost white with slight blue tint - #fcfcfd  */
    --card-foreground: 230 60% 10%; /* Near-black with blue undertones - #0a0f29 */

    --popover: 220 30% 99%; /* Same as card for consistency */
    --popover-foreground: 230 60% 10%; /* Near-black with blue undertones - #0a0f29 */
    
    --primary: 230 60% 20%; /* Deep blue - sophisticated and professional - #141f52 */
    --primary-foreground: 0 0% 100%; /* Pure white for contrast - #ffffff */

    --background: 0 0% 100%; /* Clean white background - #ffffff */
    --foreground: 230 60% 10%; /* Dark text for readability - #000833*/

    --accent: 210 100% 50%; /* Calm blue - professional and trustworthy - #0080ff*/
    --accent-foreground: 0 0% 100%; /* White for contrast - #ffffff*/

    --secondary: 210 50% 90%; /* Light blue - subtle accents -  #cce6ff*/
    --secondary-foreground: 210 100% 20%; /* Dark blue for contrast - #003366 */

    --muted: 220 20% 97%; /* Very light blue-gray - #f0f5ff */
    --muted-foreground: 230 20% 40%; /* Medium blue-gray for secondary text - #52587a */

    --border: 220 20% 90% /* Light gray - #e0e4eb */;
    --input: 220 20% 90%; /* Same as border for consistency - #e0e4eb */
    --ring: 210 100% 50%; /* Match accent color - #0080ff*/

    /* Destructive variable was already here, ensure it's what you intend or rename if danger is distinct */
    --destructive: 0 63% 42%; /* Defaulting destructive to a red, you can adjust */
    --destructive-foreground: 0 0% 100%;

    /* Added Success and Danger HSL variables */
    --success: 145 63% 42%; /* Green e.g. #33a852 */
    --success-foreground: 0 0% 100%; /* White */
    --success-light: 145 63% 95%; /* Light green background */
    --danger: 0 72% 51%; /* Red e.g. #e53935 */
    --danger-foreground: 0 0% 100%; /* White */
    --danger-light: 0 72% 95%; /* Light red background */
    --accent-light: 210 100% 95%; /* Light blue background */

    /* Chart and visualization colors */
    --chart-primary: 213 94% 68%; /* Blue #3b82f6 */
    --chart-secondary: 142 76% 36%; /* Green #22c55e */
    --chart-tertiary: 25 95% 53%; /* Orange #f97316 */
    --chart-quaternary: 262 83% 58%; /* Purple #a855f7 */

    /* Status and feedback colors */
    --info: 199 89% 48%; /* Cyan #0ea5e9 */
    --info-foreground: 0 0% 100%; /* White */
    --warning: 45 93% 47%; /* Amber #f59e0b */
    --warning-foreground: 0 0% 100%; /* White */

    /* Subject-specific colors */
    --subject-math: 213 94% 68%; /* Blue #3b82f6 */
    --subject-math-foreground: 0 0% 100%; /* White */
    --subject-science: 142 76% 36%; /* Green #22c55e */
    --subject-science-foreground: 0 0% 100%; /* White */
    --subject-english: 262 83% 58%; /* Purple #a855f7 */
    --subject-english-foreground: 0 0% 100%; /* White */
    --subject-history: 45 93% 47%; /* Amber #f59e0b */
    --subject-history-foreground: 0 0% 100%; /* White */
    --subject-computer: 173 58% 39%; /* Teal #14b8a6 */
    --subject-computer-foreground: 0 0% 100%; /* White */
    --subject-upsc: 173 58% 39%; /* Teal #14b8a6 */
    --subject-upsc-foreground: 0 0% 100%; /* White */

    /* Proficiency level colors */
    --proficiency-excellent: 262 83% 58%; /* Purple #a855f7 */
    --proficiency-excellent-foreground: 0 0% 100%; /* White */
    --proficiency-good: 213 94% 68%; /* Blue #3b82f6 */
    --proficiency-good-foreground: 0 0% 100%; /* White */
    --proficiency-average: 142 76% 36%; /* Green #22c55e */
    --proficiency-average-foreground: 0 0% 100%; /* White */
    --proficiency-needs-improvement: 45 93% 47%; /* Yellow #f59e0b */
    --proficiency-needs-improvement-foreground: 0 0% 100%; /* White */

    /* Graph and network colors */
    --graph-subject: 262 83% 58%; /* Purple #a855f7 */
    --graph-chapter: 142 76% 36%; /* Green #22c55e */
    --graph-subtopic: 45 93% 47%; /* Amber #f59e0b */
    --graph-edge-primary: 213 94% 68%; /* Blue #3b82f6 */
    --graph-edge-secondary: 142 76% 36%; /* Green #22c55e */
    --graph-edge-tertiary: 25 95% 53%; /* Orange #f97316 */

    --radius: 0.75rem; /* Slightly more rounded corners */
  }

  .dark {
    /* Rich, layered dark theme with depth and sophistication */
    --background: 222 35% 6%; /* Deep charcoal with blue undertones - #0a0d12 */
    --foreground: 210 40% 98%; /* Crisp white with slight blue tint - #f8fafc */

    /* Cards with subtle elevation and warmth */
    --card: 223 47% 11%; /* Elevated dark surface - #0f1419 */
    --card-foreground: 210 40% 95%; /* High contrast text - #f1f5f9 */

    /* Popovers matching card styling */
    --popover: 223 47% 11%; /* Match card background */
    --popover-foreground: 210 40% 95%; /* Match card text */

    /* Primary with vibrant blue accent */
    --primary: 213 94% 68%; /* Bright, accessible blue - #3b82f6 */
    --primary-foreground: 222 35% 6%; /* Dark text on bright primary */

    /* Secondary with subtle purple-blue tones */
    --secondary: 217 32% 17%; /* Rich slate with blue hints - #1e293b */
    --secondary-foreground: 210 40% 85%; /* Light text for contrast - #cbd5e1 */

    /* Muted areas with warm undertones */
    --muted: 215 25% 16%; /* Warmer muted background - #1c2532 */
    --muted-foreground: 217 10% 64%; /* Medium contrast text - #94a3b8 */

    /* Accent with energetic cyan-blue */
    --accent: 199 89% 48%; /* Vibrant cyan-blue - #0ea5e9 */
    --accent-foreground: 222 35% 6%; /* Dark text on bright accent */

    /* Borders with subtle contrast */
    --border: 217 32% 22%; /* Visible but not harsh borders - #334155 */
    --input: 217 32% 20%; /* Input backgrounds slightly darker - #2d3b4f */
    --ring: 213 94% 68%; /* Match primary for focus rings */

    /* Enhanced status colors for dark mode */
    --destructive: 0 84% 60%; /* Bright, accessible red - #ef4444 */
    --destructive-foreground: 0 0% 100%;

    --success: 142 76% 36%; /* Rich, accessible green - #16a34a */
    --success-foreground: 0 0% 100%;
    --success-light: 142 76% 20%; /* Darker green background for dark mode */

    --danger: 0 84% 60%; /* Match destructive for consistency */
    --danger-foreground: 0 0% 100%;
    --danger-light: 0 84% 20%; /* Darker red background for dark mode */
    --accent-light: 199 89% 20%; /* Darker cyan background for dark mode */

    /* Chart and visualization colors - enhanced for dark mode */
    --chart-primary: 213 94% 68%; /* Bright blue #3b82f6 */
    --chart-secondary: 142 76% 36%; /* Rich green #16a34a */
    --chart-tertiary: 25 95% 53%; /* Vibrant orange #f97316 */
    --chart-quaternary: 262 83% 58%; /* Purple #a855f7 */

    /* Status and feedback colors - enhanced for dark mode */
    --info: 199 89% 48%; /* Cyan #0ea5e9 */
    --info-foreground: 222 35% 6%; /* Dark text */
    --warning: 45 93% 47%; /* Amber #f59e0b */
    --warning-foreground: 222 35% 6%; /* Dark text */

    /* Subject-specific colors - enhanced for dark mode */
    --subject-math: 213 94% 68%; /* Bright blue #3b82f6 */
    --subject-math-foreground: 222 35% 6%; /* Dark text */
    --subject-science: 142 76% 36%; /* Rich green #16a34a */
    --subject-science-foreground: 0 0% 100%; /* White text */
    --subject-english: 262 83% 58%; /* Purple #a855f7 */
    --subject-english-foreground: 222 35% 6%; /* Dark text */
    --subject-history: 45 93% 47%; /* Amber #f59e0b */
    --subject-history-foreground: 222 35% 6%; /* Dark text */
    --subject-computer: 173 58% 39%; /* Teal #14b8a6 */
    --subject-computer-foreground: 0 0% 100%; /* White text */

    /* Proficiency level colors - enhanced for dark mode */
    --proficiency-excellent: 262 83% 58%; /* Purple #a855f7 */
    --proficiency-excellent-foreground: 222 35% 6%; /* Dark text */
    --proficiency-good: 213 94% 68%; /* Bright blue #3b82f6 */
    --proficiency-good-foreground: 222 35% 6%; /* Dark text */
    --proficiency-average: 142 76% 36%; /* Rich green #16a34a */
    --proficiency-average-foreground: 0 0% 100%; /* White text */
    --proficiency-needs-improvement: 45 93% 47%; /* Amber #f59e0b */
    --proficiency-needs-improvement-foreground: 222 35% 6%; /* Dark text */

    /* Graph and network colors - enhanced for dark mode */
    --graph-subject: 262 83% 58%; /* Purple #a855f7 */
    --graph-chapter: 142 76% 36%; /* Rich green #16a34a */
    --graph-subtopic: 45 93% 47%; /* Amber #f59e0b */
    --graph-edge-primary: 213 94% 68%; /* Bright blue #3b82f6 */
    --graph-edge-secondary: 142 76% 36%; /* Rich green #16a34a */
    --graph-edge-tertiary: 25 95% 53%; /* Vibrant orange #f97316 */

    /* Node-specific colors for dark mode */
    --color-node-subject-foreground: 222 35% 6%; /* Dark text on bright nodes */
    --color-node-chapter-foreground: 0 0% 100%; /* White text on darker nodes */
    --color-node-subtopic-foreground: 222 35% 6%; /* Dark text on bright nodes */
  }
}

@layer base {
  *,
  *::before,
  *::after {
    box-sizing: border-box;
    @apply border-[hsl(var(--border))];
  }

  body {
    @apply bg-[hsl(var(--background))] text-[hsl(var(--foreground))];
  }
}

@layer base {
  html {
    font-family: 'Nunito', 'Space Grotesk', system-ui, sans-serif;
    width: 100%;
    overflow-x: hidden;
  }

  body {
    @apply antialiased;
    width: 100%;
    overflow-x: hidden;
    min-width: 320px; /* Minimum width for mobile devices */
    position: relative;
  }



  /* Ensure images don't overflow */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Ensure containers don't overflow */
  .container {
    overflow-x: hidden;
  }




}

/* Enhanced dark mode specific styles */
@layer base {
  .dark {
    /* Add subtle texture and depth */
    body {
      background-image:
        radial-gradient(circle at 25% 25%, hsl(var(--chart-primary) / 0.03) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, hsl(var(--info) / 0.03) 0%, transparent 50%);
    }

    /* Enhanced card appearance */
    [class*="card"] {
      backdrop-filter: blur(8px);
      border: 1px solid hsl(var(--border));
      box-shadow:
        0 1px 3px 0 hsl(0 0% 0% / 0.3),
        0 0 0 1px hsl(0 0% 100% / 0.05);
    }

    /* Better button styling */
    button {
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Enhanced hover effects */
    .hover\:bg-accent:hover {
      background-color: hsl(var(--accent) / 0.1);
    }
  }
}

/* Enhanced Educational Content Formatting Styles */
@layer components {
  /* Main educational content container */
  .educational-content {
    line-height: 1.7;
    font-family: 'Nunito', 'Space Grotesk', system-ui, sans-serif;
  }

  /* Enhanced LaTeX formula styling */
  .educational-content .latex-container {
    transition: all 0.2s ease-in-out;
  }

  .educational-content .latex-container:hover {
    transform: scale(1.02);
  }

  /* Educational headers with icons */
  .educational-header-1,
  .educational-header-2,
  .educational-header-3 {
    scroll-margin-top: 2rem;
    position: relative;
  }

  .educational-header-1 .header-icon {
    animation: pulse 2s infinite;
  }

  .educational-header-2 .header-icon {
    animation: bounce 2s infinite;
  }

  .educational-header-3 .header-icon {
    animation: glow 3s ease-in-out infinite alternate;
  }

  /* Enhanced list styling for educational content */
  .educational-list {
    position: relative;
  }

  .educational-list.bullet-list {
    list-style: none;
  }

  .educational-list.bullet-list li::before {
    content: "•";
    color: hsl(var(--primary));
    font-weight: bold;
    position: absolute;
    left: 0;
    font-size: 1.2em;
    line-height: 1.2;
  }

  .educational-list.numbered-list {
    list-style: none;
    counter-reset: educational-counter;
  }

  .educational-list.numbered-list li {
    counter-increment: educational-counter;
  }

  .educational-list.numbered-list li::before {
    content: counter(educational-counter) ".";
    color: hsl(var(--primary));
    font-weight: bold;
    position: absolute;
    left: 0;
    font-size: 0.9em;
    line-height: 1.4;
  }

  /* Educational section styling */
  .educational-section {
    position: relative;
    transition: all 0.3s ease-in-out;
  }

  .educational-section:hover {
    transform: translateY(-1px);
  }

  .educational-section .section-header {
    position: relative;
    overflow: hidden;
  }

  .educational-section .section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: width 0.5s ease-in-out;
  }

  .educational-section:hover .section-header::before {
    width: 100%;
  }

  /* Enhanced inline formatting for educational terms */
  .key-term {
    position: relative;
    transition: all 0.2s ease-in-out;
    cursor: help;
  }

  .key-term:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .definition-term {
    position: relative;
    transition: all 0.2s ease-in-out;
  }

  .definition-term:hover {
    transform: scale(1.02);
  }

  .math-variable {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    transition: all 0.2s ease-in-out;
  }

  .math-variable:hover {
    transform: scale(1.1);
  }

  .formula-code {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    transition: all 0.2s ease-in-out;
  }

  .formula-code:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  }

  .math-number {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    transition: color 0.2s ease-in-out;
  }

  /* Enhanced message content wrapper */
  .enhanced-message-content {
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
  }

  /* User message content styling - force white text with maximum specificity */
  .user-message-content,
  .user-message-content *,
  .user-message-content .educational-paragraph,
  .user-message-content .educational-content,
  .user-message-content p,
  .user-message-content span,
  .user-message-content div,
  .user-message-content li,
  .user-message-content .section-content,
  .user-message-content .list-content {
    color: white !important;
  }

  /* Override all specific text-foreground classes in user messages */
  .user-message-content .educational-paragraph.text-sm.leading-relaxed,
  .user-message-content li.text-sm.leading-relaxed,
  .user-message-content .text-foreground\/90,
  .user-message-content .text-foreground\/80,
  .user-message-content [class*="text-foreground"] {
    color: white !important;
  }

  /* Ensure user message content is white in both light and dark modes */
  .dark .user-message-content,
  .dark .user-message-content *,
  .dark .user-message-content .educational-paragraph,
  .dark .user-message-content .educational-content,
  .dark .user-message-content p,
  .dark .user-message-content span,
  .dark .user-message-content div,
  .dark .user-message-content li,
  .dark .user-message-content .section-content,
  .dark .user-message-content .list-content {
    color: white !important;
  }

  /* Override all specific text-foreground classes in user messages - dark mode */
  .dark .user-message-content .educational-paragraph.text-sm.leading-relaxed,
  .dark .user-message-content li.text-sm.leading-relaxed,
  .dark .user-message-content .text-foreground\/90,
  .dark .user-message-content .text-foreground\/80,
  .dark .user-message-content [class*="text-foreground"] {
    color: white !important;
  }

  /* Ensure question buttons have proper text visibility with high specificity */
  button.suggested-question-button {
    color: hsl(var(--foreground)) !important;
  }

  button.suggested-question-button span {
    color: hsl(var(--foreground)) !important;
  }

  .dark button.suggested-question-button {
    color: hsl(var(--foreground)) !important;
  }

  .dark button.suggested-question-button span {
    color: hsl(var(--foreground)) !important;
  }

  /* Override any educational content styles that might interfere */
  .suggested-question-button * {
    color: inherit !important;
  }

  /* Ensure proper contrast in both themes */
  :root .suggested-question-button {
    color: hsl(230 60% 10%) !important; /* Light mode foreground */
  }

  .dark .suggested-question-button {
    color: hsl(210 40% 98%) !important; /* Dark mode foreground */
  }

  /* Animations for educational elements */
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    60% { transform: translateY(-2px); }
  }

  @keyframes glow {
    from { text-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
    to { text-shadow: 0 0 10px rgba(59, 130, 246, 0.6); }
  }

  /* Mobile responsiveness for educational content */
  @media (max-width: 640px) {
    .educational-content {
      font-size: 0.875rem;
      line-height: 1.6;
    }

    .educational-header-1 {
      font-size: 1.125rem;
      margin-top: 0.75rem;
      margin-bottom: 0.5rem;
    }

    .educational-header-2 {
      font-size: 1rem;
      margin-top: 0.5rem;
      margin-bottom: 0.25rem;
    }

    .educational-header-3 {
      font-size: 0.9375rem;
      margin-top: 0.25rem;
      margin-bottom: 0.125rem;
    }

    .educational-list {
      padding-left: 0.75rem;
      margin: 0.5rem 0;
    }

    .educational-section {
      margin: 0.5rem 0;
    }

    .educational-section .section-header {
      padding: 0.5rem;
    }

    .educational-section .section-content {
      margin-left: 0.5rem;
      padding: 0.25rem;
    }

    .latex-container {
      font-size: 0.8125rem;
      margin: 0.5rem 0;
    }

    .key-term,
    .definition-term,
    .math-variable,
    .formula-code {
      font-size: 0.8125rem;
      padding: 0.25rem 0.5rem;
    }

    /* Disable hover effects on mobile */
    .educational-section:hover,
    .key-term:hover,
    .definition-term:hover,
    .math-variable:hover,
    .formula-code:hover {
      transform: none;
      box-shadow: none;
    }
  }

  /* Enhanced LaTeX integration */
  .educational-content .katex {
    font-size: 1.1em;
  }

  .educational-content .katex-display {
    margin: 1.5rem 0;
    padding: 1rem;
    background: hsl(var(--accent) / 0.05);
    border-radius: 0.5rem;
    border: 1px solid hsl(var(--accent) / 0.2);
  }

  /* Light mode explicit styling for educational content */
  :root .educational-content {
    color: hsl(var(--foreground));
  }

  /* Light mode educational headers */
  :root .educational-header-1 {
    color: hsl(var(--primary)) !important;
    border-color: hsl(var(--primary) / 0.3) !important;
  }

  :root .educational-header-2 {
    color: hsl(var(--accent)) !important;
    border-color: hsl(var(--accent) / 0.3) !important;
  }

  :root .educational-header-3 {
    color: hsl(var(--secondary-foreground)) !important;
  }

  /* Light mode mathematical equations */
  :root .math-equation-container {
    background: hsl(var(--accent) / 0.05) !important;
    border-color: hsl(var(--accent) / 0.2) !important;
  }

  /* Light mode inline formatting with high contrast */
  :root .key-term {
    background: hsl(230 60% 95%) !important; /* Light blue background */
    color: hsl(230 60% 20%) !important; /* Dark blue text for high contrast */
    border: 1px solid hsl(230 60% 80%) !important;
  }

  :root .key-term:hover {
    background: hsl(230 60% 90%) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  :root .definition-term {
    background: hsl(210 50% 95%) !important; /* Light blue background */
    color: hsl(210 100% 25%) !important; /* Dark blue text for high contrast */
    border: 1px solid hsl(210 50% 80%) !important;
  }

  :root .definition-term:hover {
    background: hsl(210 50% 90%) !important;
  }

  :root .math-variable {
    background: hsl(210 100% 95%) !important; /* Light blue background */
    color: hsl(210 100% 30%) !important; /* Dark blue text for high contrast */
    border: 1px solid hsl(210 100% 80%) !important;
  }

  :root .math-variable:hover {
    background: hsl(210 100% 90%) !important;
  }

  :root .math-number {
    color: hsl(210 100% 30%) !important; /* Dark blue for high contrast */
    font-weight: 600 !important;
  }

  :root .formula-code {
    background: hsl(199 89% 95%) !important; /* Light cyan background */
    color: hsl(199 89% 25%) !important; /* Dark cyan text for high contrast */
    border-color: hsl(199 89% 80%) !important;
  }

  :root .formula-code:hover {
    background: hsl(199 89% 90%) !important;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  }

  /* Light mode list styling */
  :root .educational-list li {
    color: hsl(var(--foreground) / 0.9) !important;
  }

  :root .educational-list.bullet-list li::before {
    color: hsl(var(--primary)) !important;
  }

  :root .educational-list.numbered-list li::before {
    color: hsl(var(--primary)) !important;
  }

  /* Light mode paragraph text */
  :root .educational-paragraph {
    color: hsl(var(--foreground) / 0.9) !important;
  }

  /* Light mode section headers */
  :root .educational-section .section-header {
    background: hsl(var(--background)) !important;
    border-color: hsl(var(--border)) !important;
  }

  /* Light mode section content */
  :root .educational-section .section-content {
    background: hsl(var(--muted) / 0.5) !important;
    color: hsl(var(--foreground) / 0.9) !important;
  }

  /* Dark mode enhancements for educational content */
  .dark .educational-content {
    /* Enhanced contrast for better readability */
    color: hsl(var(--foreground));
  }

  /* Dark mode educational headers */
  .dark .educational-header-1 {
    color: hsl(var(--primary)) !important;
    border-color: hsl(var(--primary) / 0.4) !important;
  }

  .dark .educational-header-2 {
    color: hsl(var(--accent)) !important;
    border-color: hsl(var(--accent) / 0.4) !important;
  }

  .dark .educational-header-3 {
    color: hsl(var(--accent)) !important;
  }

  /* Dark mode section headers */
  .dark .educational-section .section-header {
    background: hsl(var(--card)) !important;
    border-color: hsl(var(--border)) !important;
  }

  .dark .educational-section .section-header::before {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.05), transparent);
  }

  /* Dark mode section content */
  .dark .educational-section .section-content {
    background: hsl(var(--muted) / 0.3) !important;
    color: hsl(var(--foreground) / 0.9) !important;
  }

  /* Dark mode mathematical equations */
  .dark .math-equation-container {
    background: hsl(var(--accent) / 0.1) !important;
    border-color: hsl(var(--accent) / 0.3) !important;
  }

  /* Dark mode inline formatting */
  .dark .key-term {
    background: hsl(var(--primary) / 0.2) !important;
    color: hsl(var(--primary)) !important;
  }

  .dark .key-term:hover {
    box-shadow: 0 2px 8px rgba(255,255,255,0.1);
    background: hsl(var(--primary) / 0.3) !important;
  }

  .dark .definition-term {
    background: hsl(var(--secondary) / 0.2) !important;
    color: hsl(var(--accent)) !important;
  }

  .dark .definition-term:hover {
    background: hsl(var(--secondary) / 0.3) !important;
  }

  .dark .math-variable {
    background: hsl(var(--accent) / 0.2) !important;
    color: hsl(var(--accent)) !important;
  }

  .dark .math-variable:hover {
    background: hsl(var(--accent) / 0.3) !important;
  }

  .dark .math-number {
    color: hsl(var(--accent)) !important;
  }

  .dark .formula-code {
    background: hsl(var(--info) / 0.2) !important;
    color: hsl(var(--info)) !important;
    border-color: hsl(var(--info) / 0.4) !important;
  }

  .dark .formula-code:hover {
    box-shadow: 0 2px 8px rgba(255,255,255,0.1);
    background: hsl(var(--info) / 0.3) !important;
  }

  /* Dark mode list styling */
  .dark .educational-list li {
    color: hsl(var(--foreground) / 0.9) !important;
  }

  .dark .educational-list.bullet-list li::before {
    color: hsl(var(--primary)) !important;
  }

  .dark .educational-list.numbered-list li::before {
    color: hsl(var(--primary)) !important;
  }

  /* Dark mode paragraph text */
  .dark .educational-paragraph {
    color: hsl(var(--foreground) / 0.9) !important;
  }

  /* Dark mode LaTeX containers */
  .dark .latex-container {
    background: hsl(var(--accent) / 0.1) !important;
    border-color: hsl(var(--accent) / 0.3) !important;
  }

  /* Dark mode time schedules */
  .dark .schedule-container {
    background: hsl(var(--accent) / 0.1) !important;
    border-color: hsl(var(--accent) / 0.3) !important;
  }

  .dark .schedule-time {
    background: hsl(var(--muted) / 0.3) !important;
    color: hsl(var(--muted-foreground)) !important;
  }

  /* Dark mode day headers */
  .dark .day-header {
    color: hsl(var(--primary)) !important;
  }

  .dark .day-header .day-separator {
    background: hsl(var(--border) / 0.5) !important;
  }

  /* Dark mode enhanced content wrapper */
  .dark .enhanced-content {
    color: hsl(var(--foreground)) !important;
  }

  /* Print styles for educational content */
  @media print {
    .educational-content {
      color: black !important;
      background: white !important;
    }

    .educational-section .section-header {
      border: 1px solid #ccc !important;
      background: #f5f5f5 !important;
    }

    .key-term,
    .definition-term,
    .math-variable,
    .formula-code {
      background: #f0f0f0 !important;
      border: 1px solid #ddd !important;
    }
  }
}

/* === Custom Toast Styles for react-toastify === */
@layer components {
  .Toastify__toast {
    font-family: 'Urbanist', 'Space Grotesk', system-ui, sans-serif;
    border-radius: 0.75rem;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid hsl(var(--border));
    background: hsl(var(--card));
    color: hsl(var(--foreground));
    padding: 1rem 1.25rem;
    font-size: 1rem;
    min-width: 220px;
    max-width: 350px;
    border-left-width: 6px;
  }
  /* Light mode: use *-light backgrounds for toast types */
  :root .Toastify__toast--success {
    background: hsl(var(--success-light) / 0.85);
    color: hsl(var(--success) / 0.85);
    border-color: hsl(var(--success) / 0.7);
    border-left-color: hsl(var(--success) / 0.7);
  }
  :root .Toastify__toast--error {
    background: hsl(var(--danger-light) / 0.85);
    color: hsl(var(--danger) / 0.85);
    border-color: hsl(var(--danger) / 0.7);
    border-left-color: hsl(var(--danger) / 0.7);
  }
  :root .Toastify__toast--warning {
    background: hsl(var(--warning) / 0.12);
    color: hsl(var(--warning));
    border-color: hsl(var(--warning));
    border-left-color: hsl(var(--warning));
  }
  :root .Toastify__toast--info {
    background: hsl(var(--info) / 0.10);
    color: hsl(var(--info));
    border-color: hsl(var(--info));
    border-left-color: hsl(var(--info));
  }
  :root .Toastify__toast--default {
    background: hsl(var(--card));
    color: hsl(var(--foreground));
    border-color: hsl(var(--border));
    border-left-color: hsl(var(--border));
  }
  /* Dark mode: use base color backgrounds for toast types */
  .dark .Toastify__toast--success {
    background: hsl(var(--success) / 0.7);
    color: hsl(var(--success-foreground));
    border-color: hsl(var(--success));
    border-left-color: hsl(var(--success));
  }
  .dark .Toastify__toast--error {
    background: hsl(var(--danger) / 0.7);
    color: hsl(var(--danger-foreground));
    border-color: hsl(var(--danger));
    border-left-color: hsl(var(--danger));
  }
  .dark .Toastify__toast--warning {
    background: hsl(var(--warning));
    color: hsl(var(--warning-foreground));
    border-color: hsl(var(--warning));
    border-left-color: hsl(var(--warning));
  }
  .dark .Toastify__toast--info {
    background: hsl(var(--info));
    color: hsl(var(--info-foreground));
    border-color: hsl(var(--info));
    border-left-color: hsl(var(--info));
  }
  .dark .Toastify__toast--default {
    background: hsl(var(--card));
    color: hsl(var(--foreground));
    border-color: hsl(var(--border));
    border-left-color: hsl(var(--border));
  }
  .Toastify__progress-bar {
    background: hsl(var(--accent));
    height: 4px;
    border-radius: 2px;
  }
  .Toastify__close-button {
    color: hsl(var(--muted-foreground));
    opacity: 0.7;
    transition: opacity 0.2s;
  }
  .Toastify__close-button:hover {
    opacity: 1;
    color: hsl(var(--accent));
  }
  .dark .Toastify__progress-bar {
    background: hsl(var(--accent));
  }
  .dark .Toastify__close-button {
    color: hsl(var(--muted-foreground));
  }
}
.wave-animation {
  position: absolute;
  top: -25px;
  left: 0;
  right: 0;
  width: 100%;
  height: 30px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 88.7" preserveAspectRatio="none"><path d="M800 56.9c-155.5 0-204.9-50-405.5-49.9-200 0-250 49.9-394.5 49.9v31.8h800v-.2-31.6z" fill="white" fill-opacity="0.5"/></svg>');
  background-repeat: repeat-x;
  animation: wave 4s cubic-bezier(0.36, 0.45, 0.63, 0.53) infinite,
    swell 4s ease infinite;
  transform: translate3d(0, 0, 0);
  overflow: hidden;
  pointer-events: none;
}

.wave-animation::before {
  content: "";
  position: absolute;
  top: 2px;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
  animation: wave 4s cubic-bezier(0.36, 0.45, 0.63, 0.53) -0.125s infinite,
    swell 4s ease -1.25s infinite;
  opacity: 0.6;
}

@keyframes wave {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(50%);
  }
}

@keyframes swell {
  0%,
  100% {
    transform: translateY(-6px);
  }
  50% {
    transform: translateY(4px);
  }
}

@keyframes spin-slow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.outputsvg {
  transform-origin: 50% 50%;
}

@keyframes dash {
  to {
    stroke-dashoffset: -18;
  }
}

.path-animation {
  animation: dash 3s linear infinite;
}