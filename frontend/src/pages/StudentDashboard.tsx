import { useState } from 'react';
import { ChevronDownIcon, ListBulletIcon } from '@heroicons/react/24/outline';
import { useUser } from '../contexts/userContext';
import ViewUpcomingTest from '../components/ViewUpcomingTest';
import ClassList from '../components/ClassList';

import RecentActivity from '../components/RecentActivity';
import AegisScholarLogoWithoutText from '@/assets/AegisScholarLogoIcon';
import FeedBackComp from '@/components/Feedback';
import { toast } from 'react-toastify';

const StudentDashboard = () => {
  const { user, } = useUser();
  const [isTestOpen, setIsTestOpen] = useState(false);

  return (
    <div className="w-full flex flex-col p-4 pb-6 pt-0 gap-4 min-h-screen overflow-hidden sm:overflow-auto sm:h-screen bg-background">
      {/* Header - Fixed height */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3 p-2">
          <div className='flex flex-row items-start'>
            <AegisScholarLogoWithoutText
              className="w-12 h-12 lg:hidden md:hidden mr-2"
              style={{ fill: 'var(--color-accent)' }}
            />
            <div className="flex flex-col">
              <small className="text-sm sm:text-base font-[Space_Grotesk] text-muted-foreground">Hello</small>
              <h1 className="text-xl lg:text-2xl font-[Space_Grotesk] font-bold text-primary">
                {user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : user?.username}!
              </h1>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 w-full sm:w-auto">
          <div className="relative w-full sm:w-auto">
            <button
              onClick={() => setIsTestOpen(!isTestOpen)}
              className="w-full sm:w-auto border border-border flex items-center justify-center gap-1 bg-accent/10 text-accent lg:px-4 md:px-2 sm:px-1 py-3 rounded-xl hover:bg-accent/20 transition-all duration-300"
            >
              <ListBulletIcon className="w-5 h-5" />
              <span className="font-[Space_Grotesk] whitespace-nowrap">Upcoming Test</span>
              <ChevronDownIcon className={`w-4 h-4 transition-transform duration-300 ${isTestOpen ? 'rotate-180' : ''}`} />
            </button>

            {isTestOpen && (
              <div className="absolute right-0 top-full mt-2 w-full sm:w-96 max-w-xs bg-card rounded-xl shadow-xl border border-border z-50">
                <ViewUpcomingTest />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content - Reordered for mobile */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-4 min-h-0">
        {/* Classes Section - Moves below stats on mobile */}
        <div className="order-1 lg:order-2 lg:col-span-3 flex flex-col">
          <div className="flex-1 overflow-auto">
            <ClassList />
          </div>
        </div>

        {/* Left Column - Moves below classes on mobile */}
        <div className="order-2 lg:order-1 col-span-1 grid grid-rows-2 gap-4 min-h-0 h-full">
          {/* Recent Activities Panel */}
          <div className="overflow-hidden h-[300px] md:h-[350px] lg:h-full lg:border-r lg:pr-4">
            <RecentActivity />
          </div>
        </div>
      </div>
      <FeedBackComp id={user?.id} role={user?.role} />
    </div>
  );
};

export default StudentDashboard;
