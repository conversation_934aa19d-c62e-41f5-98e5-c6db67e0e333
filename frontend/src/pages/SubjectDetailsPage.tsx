import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  BookOpenIcon, 
  ArrowLeftIcon, 
  ChartBarIcon, 
  TableCellsIcon, 
  ChevronDownIcon, 
  ChevronRightIcon, 
  CheckCircleIcon, 
  BookmarkIcon, 
  PencilSquareIcon, 
  ArrowDownIcon, 
  ArrowUpIcon
} from '@heroicons/react/24/outline';
import { useUser } from '../contexts/userContext';
import { Subject, StudentAnalytics } from '../types/student';
import SubjectDetailsGraphWithProvider from '../components/SubjectDetailsGraph';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import LoadingSpinner from '@/components/LoadingSpinner';
import { fetchWithCache } from '@/utils/cacheUtil';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../shadcn/components/ui/tabs';
import { usePageRefresh } from '../hooks/usePageRefresh';

const SubjectDetailsPage = () => {
  const { classId } = useParams<{ classId: string }>();
  const axiosPrivate = useAxiosPrivate();
  const { user } = useUser();
  const navigate = useNavigate();

  const [subject, setSubject] = useState<Subject | null>(null);
  const [studentAnalytics, setStudentAnalytics] = useState<StudentAnalytics | null>(null);
  const [topicArray, setTopicArray] = useState<any[]>([]);
  const [subtopicArray, setSubtopicArray] = useState<any[]>([]);
  const [expandedTopics, setExpandedTopics] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [graphData, setGraphData] = useState<any>(null);

  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [sortBy, setSortBy] = useState<'topic' | 'proficiency' | 'status'>('topic');

  usePageRefresh();

  useEffect(() => {
    const fetchDetails = async () => {
      try {
        const subjectName = user?.subjects?.find((subject) => subject.subjectClassId === classId)?.subjectName;
        console.error(`subjectName: ${subjectName}`);
        const data = await fetchWithCache(axiosPrivate, `/api/knowledgeGraph/${user?.id}/${subjectName}`);
        setGraphData(data);

        const curriculumNodes = data?.curriculumProgress;
        const topics = curriculumNodes
          .filter((currNode: any) => currNode.nodeId.type === "Chapter")
          .map((topic: any) => topic);

        const subtopics = curriculumNodes
          .filter((currNode: any) => currNode.nodeId.type === "Subtopic")
          .map((subtopic: any) => subtopic);

        // console.error('Topic Array:', topics);
        // console.error('Subtopic Array:', subtopics);
        setTopicArray(topics);
        setSubtopicArray(subtopics);
      } catch (error) {
        console.error('Error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDetails();
    const sub = user?.subjects?.find((subject) => subject.subjectClassId === classId);
    if (sub) {
      setSubject(sub);
      setStudentAnalytics(sub.analytics);
    }
  }, [user]);

  const handleSort = (column: 'topic' | 'proficiency' | 'status') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  const getProficiencyColor = (proficiency: number): string => {
    if (proficiency >= 0.9) return 'bg-[hsl(var(--proficiency-excellent)/0.2)] text-[hsl(var(--proficiency-excellent))]';
    if (proficiency >= 0.8) return 'bg-[hsl(var(--proficiency-good)/0.2)] text-[hsl(var(--proficiency-good))]';
    if (proficiency >= 0.7) return 'bg-[hsl(var(--proficiency-average)/0.2)] text-[hsl(var(--proficiency-average))]';
    return 'bg-[hsl(var(--proficiency-needs-improvement)/0.2)] text-[hsl(var(--proficiency-needs-improvement))]';
  };

  const getProficiencyIcon = (proficiency: number) => {
    if (proficiency >= 0.9) return <CheckCircleIcon className="w-4 h-4 text-[hsl(var(--proficiency-excellent))]" />;
    if (proficiency >= 0.8) return <CheckCircleIcon className="w-4 h-4 text-[hsl(var(--proficiency-good))]" />;
    if (proficiency >= 0.7) return <CheckCircleIcon className="w-4 h-4 text-[hsl(var(--proficiency-average))]" />;
    return <PencilSquareIcon className="w-4 h-4 text-[hsl(var(--proficiency-needs-improvement))]" />;
  };

  const toggleExpand = (topicId: string) => {
    setExpandedTopics(prev => {
      const newSet = new Set(prev);
      if (newSet.has(topicId)) {
        newSet.delete(topicId);
      } else {
        newSet.add(topicId);
      }
      return newSet;
    });
  };

  // Get status priority for sorting
  const getStatusPriority = (status: string): number => {
    switch (status) {
      case 'Completed': return 3;
      case 'In Progress': return 2;
      case 'Not Started': return 1;
      default: return 0;
    }
  };

  const AnalyticsTable = ({ topicArray, subtopicArray }: { topicArray: any[], subtopicArray: any[] }) => {
    // Sort the topic array based on sortBy and sortOrder
    const sortedTopicArray = [...topicArray].sort((a, b) => {
      if (sortBy === 'topic') {
        const nameA = a.nodeId.name.toLowerCase();
        const nameB = b.nodeId.name.toLowerCase();
        return sortOrder === 'asc'
          ? nameA.localeCompare(nameB)
          : nameB.localeCompare(nameA);
      } else if (sortBy === 'status') {
        // Sort by status priority
        const statusA = getStatusPriority(a.status);
        const statusB = getStatusPriority(b.status);
        return sortOrder === 'asc'
          ? statusA - statusB
          : statusB - statusA;
      } else {
        // Sort by proficiency
        return sortOrder === 'asc'
          ? a.proficiency - b.proficiency
          : b.proficiency - a.proficiency;
      }
    });

    return (
      <div className="h-full">
        {loading ? (
          <div className="flex items-center justify-center h-screen bg-secondary">
            <LoadingSpinner />
          </div>
        ) : (
          <div className="rounded-lg border border-border shadow-sm overflow-x-auto">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-card sticky top-0 z-10">
                <tr>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer hover:bg-secondary"
                    onClick={() => handleSort('topic')}
                  >
                    <div className="flex items-center gap-1">
                      <span>Topic</span>
                      {sortBy === 'topic' && (
                        <span className="text-accent">
                          {sortOrder === 'asc' ? <ArrowDownIcon className='h-4 w-4' /> : <ArrowUpIcon className='h-4 w-4' />}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer hover:bg-secondary"
                    onClick={() => handleSort('proficiency')}
                  >
                    <div className="flex items-center gap-1">
                      <span>Proficiency</span>
                      {sortBy === 'proficiency' && (
                        <span className="text-accent">
                          {sortOrder === 'asc' ? <ArrowDownIcon className='h-4 w-4' /> : <ArrowUpIcon className='h-4 w-4' />}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider cursor-pointer hover:bg-secondary"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center gap-1">
                      <span>Status</span>
                      {sortBy === 'status' && (
                        <span className="text-accent">
                          {sortOrder === 'asc' ? <ArrowDownIcon className='h-4 w-4' /> : <ArrowUpIcon className='h-4 w-4' />}
                        </span>
                      )}
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-card divide-y divide-border">
                {sortedTopicArray.map((item, index) => (
                  <React.Fragment key={index}>
                    <tr
                      onClick={() => toggleExpand(item.nodeId._id)}
                      className={`cursor-pointer transition-colors hover:bg-secondary ${expandedTopics.has(item.nodeId._id) ? 'bg-secondary' : ''}`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2 text-sm font-medium text-primary">
                          {expandedTopics.has(item.nodeId._id) ?
                            <ChevronDownIcon className="w-4 h-4 text-muted-foreground" /> :
                            <ChevronRightIcon className="w-4 h-4 text-muted-foreground" />
                          }
                          <span>{item.nodeId.name}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {getProficiencyIcon(item.proficiency)}
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getProficiencyColor(item.proficiency)}`}>
                            {(item.proficiency).toFixed(1)}%
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          item.status === 'Completed'
                            ? 'bg-[hsl(var(--success)/0.2)] text-[hsl(var(--success))]'
                            : item.status === 'In Progress'
                              ? 'bg-[hsl(var(--info)/0.2)] text-[hsl(var(--info))]'
                              : 'bg-[hsl(var(--warning)/0.2)] text-[hsl(var(--warning))]'
                        }`}>
                          {item.status}
                        </span>
                      </td>
                    </tr>
                    {/* Subtopics with connecting lines */}
                    {expandedTopics.has(item.nodeId._id) && item.nodeId.children.map((subtopic: any, subIndex: number) => {
                      const isLastSubtopic = subIndex === item.nodeId.children.length - 1;
                      // console.error('Subtopic:', subtopic);
                      // console.error('item:', item);
                      const foundSubtopic = subtopicArray.find((subtopicItem) => subtopicItem.nodeId._id === subtopic);
                      // console.error('Found Subtopic:', foundSubtopic);
                      return (
                        <tr key={foundSubtopic._id} className="bg-muted/50 transition-colors hover:bg-muted">
                          <td className="relative px-6 py-4 whitespace-nowrap">
                            {/* Connecting lines */}
                            <div className="absolute left-8 top-0 bottom-0 border-l-2 border-border"
                              style={{
                                height: isLastSubtopic ? '50%' : '100%',
                              }}></div>

                            <div className="absolute left-8 top-1/2 w-4 border-t-2 border-border"></div>

                            <div className="flex items-center gap-2 pl-10 text-sm text-muted-foreground">
                              <span>{foundSubtopic.nodeId.name}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {foundSubtopic ? (
                              <div className="flex items-center gap-2">
                                {getProficiencyIcon(foundSubtopic.proficiency)}
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getProficiencyColor(foundSubtopic.proficiency)}`}>
                                  {(foundSubtopic.proficiency).toFixed(1)}%
                                </span>
                              </div>
                            ) : (
                              <span className="px-2 py-1 text-xs font-medium rounded-full bg-muted text-muted-foreground">N/A</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {foundSubtopic ? (
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                foundSubtopic.status === 'Completed'
                                  ? 'bg-[hsl(var(--success)/0.2)] text-[hsl(var(--success))]'
                                  : foundSubtopic.status === 'In Progress'
                                    ? 'bg-[hsl(var(--info)/0.2)] text-[hsl(var(--info))]'
                                    : 'bg-[hsl(var(--warning)/0.2)] text-[hsl(var(--warning))]'
                              }`}>
                                {foundSubtopic.status}
                              </span>
                            ) : (
                              <span className="px-2 py-1 text-xs font-medium rounded-full bg-muted text-muted-foreground">N/A</span>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto pr-4 pl-3 pb-16 lg:pb-8 py-8">
        <div className="flex items-center gap-4 mb-8">
          <button
            onClick={handleBack}
            className="p-2 rounded-full hover:bg-secondary transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-primary" />
          </button>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold text-primary">
              {subject?.subjectName || 'Loading...'}
            </h1>
          </div>
        </div>

        <Tabs defaultValue="table" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="table" className="flex items-center gap-2">
              <TableCellsIcon className="w-4 h-4" />
              Table View
            </TabsTrigger>
            <TabsTrigger value="graph" className="flex items-center gap-2">
              <ChartBarIcon className="w-4 h-4" />
              Graph View
            </TabsTrigger>
          </TabsList>

          <TabsContent value="table" className="mt-4">
            <AnalyticsTable topicArray={topicArray} subtopicArray={subtopicArray} />
          </TabsContent>

          <TabsContent value="graph" className="mt-4">
            <div className="h-[600px] bg-card rounded-lg border border-border shadow-sm">
              <SubjectDetailsGraphWithProvider graphData={graphData} />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SubjectDetailsPage;
