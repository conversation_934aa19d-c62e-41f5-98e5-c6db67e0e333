import { fetchWithCache } from "@/utils/cacheUtil";
import { ExclamationCircleIcon, CalendarIcon } from "@heroicons/react/24/outline";
import React, { useEffect, useState, useCallback, useMemo } from "react";
import TopicTagsInput from "../components/TopicTagInput";
import { useLocation, useNavigate } from "react-router-dom";
import { useUser } from "../contexts/userContext";
import { useAxiosPrivate } from "../hooks/useAxiosPrivate";
import { ScheduleTestFormType } from '@/types/scheduleTestFormType';

import { toast } from "react-toastify";

interface ValidationErrors {
  [key: string]: string;
}

// Constants for better maintainability
const CONSTANTS = {
  DURATION: { MIN: 15, MAX: 180, DEFAULT: 60 },
  QUESTIONS: { MIN: 5, MAX: 30, DEFAULT: 20 },
  MARKS: { TOTAL_DEFAULT: 100, PASSING_DEFAULT: 40 },
  TOAST_DELAY: 2000,
  TEST_TYPES: [
    { value: "", label: "Select Test Type" },
    { value: "personalized", label: "Personalized" },
    { value: "generic", label: "Generic" },
    { value: "diagnostic", label: "Diagnostic" }
  ],
  TEST_TYPES_INDIVIDUAL: [
    { value: "", label: "Select Test Type" },
    { value: "personalizedIndividual", label: "Personalized Individual" },
    { value: "diagnostic", label: "Diagnostic" }
  ]
} as const;

// Helper function to get current local date and time strings with buffer
const getCurrentLocalDateTimeStrings = () => {
  const now = new Date();
  // Add 10 minutes buffer to ensure the test doesn't become inactive immediately
  const bufferedTime = new Date(now.getTime() + 10 * 60 * 1000);
  const localDateTime = new Date(bufferedTime.getTime() - bufferedTime.getTimezoneOffset() * 60000);
  const isoString = localDateTime.toISOString();
  return {
    date: isoString.slice(0, 10),
    time: isoString.slice(11, 16),
  };
};

const ScheduleTestForm: React.FC = () => {
  const axiosPrivate = useAxiosPrivate();
  const navigate = useNavigate();
  const { user } = useUser();
  const location = useLocation();

  const testTypeOptions = useMemo(() => {
    return user?.schoolCode ? CONSTANTS.TEST_TYPES : CONSTANTS.TEST_TYPES_INDIVIDUAL;
  }, [user?.schoolCode]);



  const initialDateTime = useMemo(() => getCurrentLocalDateTimeStrings(), []);

  const [scheduleForm, setScheduleForm] = useState<ScheduleTestFormType>({
    class: "",
    subject: "",
    topics: [],
    date: initialDateTime.date,
    startTime: initialDateTime.time,
    duration: CONSTANTS.DURATION.DEFAULT,
    numberOfQuestions: CONSTANTS.QUESTIONS.DEFAULT,
    totalMarks: CONSTANTS.MARKS.TOTAL_DEFAULT,
    passingMarks: CONSTANTS.MARKS.PASSING_DEFAULT,
    instructions: "",
    testType: "",
  });

  useEffect(() => {
    if (!scheduleForm.testType && testTypeOptions.length > 0) {
      setScheduleForm((prev) => ({
        ...prev,
        testType: testTypeOptions[0].value
      }));
    }
  }, [testTypeOptions, scheduleForm.testType]);

  const [topiclist, settopiclist] = useState<string[]>([]);

  const [isLoading, setIsLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});

  // Local state for intuitive number input UX
  const [durationInput, setDurationInput] = useState(scheduleForm.duration.toString());
  const [questionsInput, setQuestionsInput] = useState(scheduleForm.numberOfQuestions.toString());

  // Sync local input state with form state if test type changes or form resets
  useEffect(() => {
    setDurationInput(scheduleForm.duration.toString());
    setQuestionsInput(scheduleForm.numberOfQuestions.toString());
  }, [scheduleForm.duration, scheduleForm.numberOfQuestions]);

  // Validation function
  const validateForm = useCallback((): ValidationErrors => {
    const errors: ValidationErrors = {};

    if (!scheduleForm.class.trim()) {
      errors.class = "Class is required";
    }

    if (!scheduleForm.subject.trim()) {
      errors.subject = "Subject is required";
    }

    if (scheduleForm.testType !== 'diagnostic' && scheduleForm.topics.length === 0) {
      errors.topics = "At least one topic is required";
    }

    if (!scheduleForm.date) {
      errors.date = "Date is required";
    } else {
      const selectedDate = new Date(scheduleForm.date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (selectedDate < today) {
        errors.date = "Date cannot be in the past";
      }
    }

    if (!scheduleForm.startTime) {
      errors.startTime = "Start time is required";
    } else {
      // Validate that the selected date and time is at least 5 minutes in the future
      const selectedDateTime = new Date(scheduleForm.date + 'T' + scheduleForm.startTime);
      const now = new Date();
      const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes buffer

      if (selectedDateTime < fiveMinutesFromNow) {
        errors.startTime = "Start time must be at least 5 minutes from now";
      }
    }

    if (scheduleForm.duration < CONSTANTS.DURATION.MIN || scheduleForm.duration > CONSTANTS.DURATION.MAX) {
      errors.duration = `Duration must be between ${CONSTANTS.DURATION.MIN} and ${CONSTANTS.DURATION.MAX} minutes`;
    }

    if (scheduleForm.numberOfQuestions < CONSTANTS.QUESTIONS.MIN || scheduleForm.numberOfQuestions > CONSTANTS.QUESTIONS.MAX) {
      errors.numberOfQuestions = `Number of questions must be between ${CONSTANTS.QUESTIONS.MIN} and ${CONSTANTS.QUESTIONS.MAX}`;
    }

    return errors;
  }, [scheduleForm]);

  const handleScheduleFormChange = useCallback((field: keyof ScheduleTestFormType, value: any) => {
    // For duration and numberOfQuestions, update local input state as well
    if (field === "duration") {
      setDurationInput(value);
    }
    if (field === "numberOfQuestions") {
      setQuestionsInput(value);
    }

    setScheduleForm((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);

  // On blur, parse and clamp values, update form state
  const handleDurationBlur = () => {
    let val = parseInt(durationInput, 10);
    if (isNaN(val)) val = CONSTANTS.DURATION.MIN;
    val = Math.max(CONSTANTS.DURATION.MIN, Math.min(CONSTANTS.DURATION.MAX, val));
    setDurationInput(val.toString());
    setScheduleForm((prev) => ({ ...prev, duration: val }));
  };


  const fetchSuggestionsTopics = async (className: string, subject: string) => {
    try {
      if (!className || !subject) {
        return;
      }
      // const response = await axiosPrivate.get('/api/tslist/getTopics/', {classid: className, subject: subject});
      const response = await fetchWithCache(axiosPrivate, `/api/tslist/getTopics/${className}/${subject}`);
      settopiclist(response);
    } catch (error) {
      console.error(`[ERROR] fetching topics for the said subject: ${error}`);
    }
  }
  useEffect(() => {
    fetchSuggestionsTopics(scheduleForm.class, scheduleForm.subject);
  }, [scheduleForm.class, scheduleForm.subject])


  const handleQuestionsBlur = () => {
    let val = parseInt(questionsInput, 10);
    if (isNaN(val)) val = CONSTANTS.QUESTIONS.MIN;
    val = Math.max(CONSTANTS.QUESTIONS.MIN, Math.min(CONSTANTS.QUESTIONS.MAX, val));
    setQuestionsInput(val.toString());
    setScheduleForm((prev) => ({ ...prev, numberOfQuestions: val }));
  };


  // Refactored: fetchAndSubmitTest now accepts loadingToastId for toast updates
  const fetchAndSubmitTest = useCallback(async (loadingToastId?: any) => {
    try {
      console.log("[INFO] Fetching and submitting test");

      const recommendResponse = await axiosPrivate.post('/api/recommend', {
        numQuestions: scheduleForm.numberOfQuestions,
        topics: scheduleForm.topics,
        classId: scheduleForm.class,
        testType: scheduleForm.testType,
        subject: scheduleForm.subject,
        userid: user?.id,
      });

      const parsedQuestions = JSON.parse(recommendResponse.data?.recommendations || '[]');

      if (!parsedQuestions || parsedQuestions.length === 0) {
        throw new Error("No questions were generated");
      }

      let response = null;

      if (scheduleForm.testType === 'diagnostic') {
        const testData = {
          ...scheduleForm,
          numberOfQuestions: parsedQuestions.length,
          questions: parsedQuestions.map((q: { _id: any; }) => q._id),
          teacherId: user?.id
        };
        response = await axiosPrivate.post('/api/test/create', { testData });
      } else if (scheduleForm.testType === 'personalized') {
        const testData = {
          ...scheduleForm,
          numberOfQuestions: parsedQuestions[0]?.questions?.length || parsedQuestions.length,
          teacherId: user?.id,
          questions: parsedQuestions,
        };
        response = await axiosPrivate.post('/api/test/createPersonalizedTest', { testData });
      } else if (scheduleForm.testType === 'personalizedIndividual') {
        const testData = {
          ...scheduleForm,
          numberOfQuestions: parsedQuestions[0]?.questions?.length || parsedQuestions.length,
          teacherId: user?.id,
          questions: parsedQuestions,
        };
        response = await axiosPrivate.post('/api/test/createPersonalizedTestIndividual', { testData });
      }

      if (response?.status === 201) {
        toast.update(loadingToastId, {
          render: 'Test generated and scheduled successfully!',
          type: 'success',
          isLoading: false,
          autoClose: CONSTANTS.TOAST_DELAY
        });
        setTimeout(() => {
          navigate('/student-dashboard', { replace: true });
          window.location.reload();
        }, CONSTANTS.TOAST_DELAY);
      } else {
        throw new Error("Failed to create test");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      toast.update(loadingToastId, {
        render: `Error scheduling test: ${errorMessage}`,
        type: 'error',
        isLoading: false,
        autoClose: CONSTANTS.TOAST_DELAY
      });
      throw error; // propagate for any additional handling
    }
  }, [scheduleForm, axiosPrivate, user?.id, navigate]);

  const handleScheduleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      toast.error("Please fix the form errors before submitting");
      return;
    }

    setIsLoading(true);
    let loadingToast: any;
    try {
      loadingToast = toast.loading("Generating test questions...", {
        autoClose: false
      });

      // Simulate processing time for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      if (scheduleForm.testType === 'generic') {
        toast.update(loadingToast, {
          render: "Test questions generated successfully!",
          type: "success",
          isLoading: false,
          autoClose: CONSTANTS.TOAST_DELAY,
        });
        setTimeout(() => {
          navigate("/reviewTest", { state: scheduleForm });
        }, CONSTANTS.TOAST_DELAY);
      } else {
        await fetchAndSubmitTest(loadingToast);
      }
    } catch (error) {
      // Error toast is already handled in fetchAndSubmitTest or here
    } finally {
      setIsLoading(false);
    }
  }, [scheduleForm, validateForm, fetchAndSubmitTest, navigate]);

  // Form field error component
  const FieldError: React.FC<{ error?: string }> = ({ error }) => {
    if (!error) return null;
    return (
      <p className="mt-1 text-xs text-[hsl(var(--danger))] flex items-center gap-1">
        <ExclamationCircleIcon className="h-3 w-3" />
        {error}
      </p>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-background border-b border-border sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <CalendarIcon className="h-5 w-5 text-accent" />
              <h1 className="text-xl font-bold text-primary">Schedule New Test</h1>
            </div>

          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 mb-12">
        <form id="schedule-test-form" onSubmit={handleScheduleSubmit} className="space-y-6">

          {/* Form Section */}
          <div className="bg-card rounded-lg border border-border shadow-sm overflow-hidden">
            <div className="p-6 space-y-6">
              {/* Class, Subject, Test Type Row */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <label htmlFor="class-select" className="text-sm font-medium text-foreground flex items-center">
                    Class <span className="text-[hsl(var(--danger))] ml-1">*</span>
                  </label>
                  <div className="relative">
                    <select
                      id="class-select"
                      className={`w-full h-12 rounded-lg border bg-background px-4 text-sm focus:ring-1 focus:ring-accent focus:border-accent focus:outline-hidden transition-all duration-200 appearance-none ${validationErrors.class ? 'border-[hsl(var(--danger)/0.5)] focus:border-[hsl(var(--danger))]' : 'border-border hover:border-muted-foreground'
                        }`}
                      value={scheduleForm.class}
                      onChange={(e) => handleScheduleFormChange("class", e.target.value)}
                      required
                      disabled={isLoading}
                    >
                      <option value="">Choose a class</option>
                      {user?.classes?.map((cls) => (
                        <option key={cls._id} value={cls._id}>
                          {cls.className}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <svg className="w-4 h-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                  <FieldError error={validationErrors.class} />
                </div>

                <div className="space-y-2">
                  <label htmlFor="subject-select" className="text-sm font-medium text-foreground flex items-center">
                    Subject <span className="text-[hsl(var(--danger))] ml-1">*</span>
                  </label>
                  <div className="relative">
                    <select
                      id="subject-select"
                      className={`w-full h-12 rounded-lg border bg-background px-4 text-sm focus:ring-1 focus:ring-accent focus:border-accent focus:outline-hidden transition-all duration-200 appearance-none ${validationErrors.subject ? 'border-[hsl(var(--danger)/0.5)] focus:border-[hsl(var(--danger))]' : 'border-border hover:border-muted-foreground'
                        }`}
                      value={scheduleForm.subject}
                      onChange={(e) => handleScheduleFormChange("subject", e.target.value)}
                      required
                      disabled={isLoading}
                    >
                      <option value="">Choose a subject</option>
                      {user?.classes?.map((cls) => (
                        <option key={cls._id} value={cls.subject}>
                          {cls.subject}
                        </option>
                      ))

                      }
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <svg className="w-4 h-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                  <FieldError error={validationErrors.subject} />
                </div>

                <div className="space-y-2">
                  <label htmlFor="test-type-select" className="text-sm font-medium text-foreground flex items-center">
                    Test Type <span className="text-[hsl(var(--danger))] ml-1">*</span>
                  </label>
                  <div className="relative">
                    <select
                      id="test-type-select"
                      className="w-full h-12 rounded-lg border border-border bg-background px-4 text-sm focus:ring-1 focus:ring-accent focus:border-accent focus:outline-hidden transition-all duration-200 appearance-none hover:border-muted-foreground"
                      value={scheduleForm.testType}
                      onChange={(e) => handleScheduleFormChange("testType", e.target.value)}
                      required
                      disabled={isLoading}
                    >
                      {testTypeOptions.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>

                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <svg className="w-4 h-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Topics Section */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground flex items-center">
                  Topics/Chapters
                  {scheduleForm.testType !== 'diagnostic' && <span className="text-[hsl(var(--danger))] ml-1">*</span>}
                </label>
                <div className="relative">
                  <TopicTagsInput
                    value={scheduleForm.topics}
                    onChange={(topics) => handleScheduleFormChange("topics", topics)}
                    disabled={scheduleForm.testType === 'diagnostic' || isLoading}
                    topicList={topiclist}
                  />
                </div>
                <FieldError error={validationErrors.topics} />
                {scheduleForm.testType === 'diagnostic' && (
                  <div className="flex items-center text-xs text-[hsl(var(--warning))] bg-[hsl(var(--warning)/0.1)] px-3 py-2 rounded-lg">
                    <ExclamationCircleIcon className="h-4 w-4 mr-2" />
                    Topics are automatically selected for diagnostic tests
                  </div>
                )}
              </div>

              {/* Date and Time Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="test-date" className="text-sm font-medium text-foreground flex items-center">
                    Test Date <span className="text-[hsl(var(--danger))] ml-1">*</span>
                  </label>
                  <div className="relative">
                    <input
                      id="test-date"
                      type="date"
                      className={`w-full h-12 rounded-lg border bg-background px-4 text-sm focus:ring-1 focus:ring-accent focus:border-accent focus:outline-hidden transition-all duration-200 ${validationErrors.date ? 'border-[hsl(var(--danger)/0.5)] focus:border-[hsl(var(--danger))]' : 'border-border hover:border-muted-foreground'
                        }`}
                      value={scheduleForm.date}
                      min={getCurrentLocalDateTimeStrings().date}
                      onChange={(e) => handleScheduleFormChange("date", e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </div>
                  <FieldError error={validationErrors.date} />
                </div>


                <div className="space-y-2">
                  <label htmlFor="start-time" className="text-sm font-medium text-foreground flex items-center">
                    Start Time <span className="text-[hsl(var(--danger))] ml-1">*</span>
                  </label>
                  <div className="relative">
                    <input
                      id="start-time"
                      type="time"
                      className={`w-full h-12 rounded-lg border bg-background px-4 text-sm focus:ring-1 focus:ring-accent focus:border-accent focus:outline-hidden transition-all duration-200 ${validationErrors.startTime ? 'border-[hsl(var(--danger)/0.5)] focus:border-[hsl(var(--danger))]' : 'border-border hover:border-muted-foreground'
                        }`}
                      value={scheduleForm.startTime}
                      onChange={(e) => handleScheduleFormChange("startTime", e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </div>
                  <FieldError error={validationErrors.startTime} />
                </div>
              </div>


              {/* Duration and Questions Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="duration" className="text-sm font-medium text-foreground flex items-center">
                    Duration (minutes) <span className="text-[hsl(var(--danger))] ml-1">*</span>
                  </label>
                  <div className="relative">
                    <input
                      id="duration"
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      min={CONSTANTS.DURATION.MIN}
                      max={CONSTANTS.DURATION.MAX}
                      className={`w-full h-12 rounded-lg border bg-background px-4 text-sm focus:ring-1 focus:ring-accent focus:border-accent focus:outline-hidden transition-all duration-200 ${validationErrors.duration ? 'border-[hsl(var(--danger)/0.5)] focus:border-[hsl(var(--danger))]' : 'border-border hover:border-muted-foreground'
                        }`}
                      value={durationInput}
                      onChange={(e) => {
                        // Only allow digits, no leading zeros unless the value is exactly "0"
                        let val = e.target.value.replace(/[^0-9]/g, "");
                        if (val.length > 1 && val.startsWith("0")) val = val.replace(/^0+/, "");
                        setDurationInput(val);
                        // Don't update form state until blur or valid number
                        if (val === "") {
                          setScheduleForm((prev) => ({ ...prev, duration: 0 }));
                        }
                      }}
                      onBlur={handleDurationBlur}
                      required
                      disabled={isLoading}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <span className="text-xs text-muted-foreground">min</span>
                    </div>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Minimum: {CONSTANTS.DURATION.MIN} min</span>
                    <span>Maximum: {CONSTANTS.DURATION.MAX} min</span>
                  </div>
                  <FieldError error={validationErrors.duration} />
                </div>

                <div className="space-y-2">
                  <label htmlFor="number-of-questions" className="text-sm font-medium text-foreground flex items-center">
                    Number of Questions <span className="text-[hsl(var(--danger))] ml-1">*</span>
                  </label>
                  <div className="relative">
                    <input
                      id="number-of-questions"
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      min={CONSTANTS.QUESTIONS.MIN}
                      max={CONSTANTS.QUESTIONS.MAX}
                      className={`w-full h-12 rounded-lg border bg-background px-4 text-sm focus:ring-1 focus:ring-accent focus:border-accent focus:outline-hidden transition-all duration-200 ${validationErrors.numberOfQuestions ? 'border-[hsl(var(--danger)/0.5)] focus:border-[hsl(var(--danger))]' : 'border-border hover:border-muted-foreground'
                        }`}
                      value={questionsInput}
                      onChange={(e) => {
                        let val = e.target.value.replace(/[^0-9]/g, "");
                        if (val.length > 1 && val.startsWith("0")) val = val.replace(/^0+/, "");
                        setQuestionsInput(val);
                        if (val === "") {
                          setScheduleForm((prev) => ({ ...prev, numberOfQuestions: 0 }));
                        }
                      }}
                      onBlur={handleQuestionsBlur}
                      required
                      disabled={isLoading}
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <span className="text-xs text-muted-foreground">qs</span>
                    </div>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Minimum: {CONSTANTS.QUESTIONS.MIN}</span>
                    <span>Maximum: {CONSTANTS.QUESTIONS.MAX}</span>
                  </div>
                  <FieldError error={validationErrors.numberOfQuestions} />
                </div>
              </div>

              {/* Instructions */}
              <div className="space-y-2">
                <label htmlFor="test-instructions" className="text-sm font-medium text-foreground">
                  Test Instructions
                </label>
                <textarea
                  id="test-instructions"
                  rows={4}
                  className="w-full rounded-lg border border-border bg-background px-4 py-3 text-sm focus:ring-1 focus:ring-accent focus:border-accent focus:outline-hidden transition-all duration-200 hover:border-muted-foreground resize-none"
                  placeholder="Enter any specific instructions for students taking this test..."
                  value={scheduleForm.instructions}
                  onChange={(e) => handleScheduleFormChange('instructions', e.target.value)}
                  disabled={isLoading}
                />
                <p className="text-xs text-muted-foreground">Optional: Provide additional context or guidelines for students</p>
              </div>
            </div>
          </div>

          {/* Quick Stats Card */}
          <div className="bg-accent/10 rounded-lg border border-accent/20 p-4">
            {/* <h3 className="text-lg font-semibold text-primary mb-2 pl-4">Test Overview</h3> */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-accent">{scheduleForm.duration}</div>
                <div className="text-xs text-muted-foreground">Minutes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-accent">{scheduleForm.numberOfQuestions}</div>
                <div className="text-xs text-muted-foreground">Questions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-accent">{scheduleForm.topics.length}</div>
                <div className="text-xs text-muted-foreground">Topics</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-accent">{scheduleForm.testType}</div>
                <div className="text-xs text-muted-foreground">Type</div>
              </div>
            </div>
          </div>
        </form>
      </div>

      {/* Fixed Footer */}
      <div className="bg-background border-t border-border p-4 pb-6 lg:pb-4 sticky bottom-14">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <ExclamationCircleIcon className="h-4 w-4 text-[hsl(var(--warning))]" />
              <span>Fields marked with <span className="text-[hsl(var(--danger))]">*</span> are required</span>
            </div>

            <button
              type="submit"
              form="schedule-test-form"
              disabled={isLoading}
              className={`w-full sm:w-auto min-w-[200px] h-12 rounded-lg px-6 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-2 focus:ring-accent ${isLoading
                ? 'bg-muted text-muted-foreground cursor-not-allowed'
                : 'bg-accent text-accent-foreground hover:bg-accent/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98] shadow-md'
                }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-border/30 border-t-border rounded-full animate-spin"></div>
                  Scheduling Test...
                </div>
              ) : (
                <div className="flex items-center justify-center gap-2">
                  <CalendarIcon className="h-4 w-4" />
                  Schedule Test
                </div>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleTestForm;
