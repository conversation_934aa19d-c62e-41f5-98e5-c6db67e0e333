import React, { useState, useEffect } from 'react';
import * as XLSX from 'xlsx';
import { UploadCloud, FileText, Users } from 'lucide-react';
import { toast, ToastContainer } from 'react-toastify';
import { fetchWithCache } from '@/utils/cacheUtil'; // Assuming this is correctly set up
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate'; // Assuming this is correctly set up
import { FeedbackCard } from '@/components/FeedbackCard';
import jsPDF from 'jspdf';
import ThemeToggle from '@/components/ThemeToggle';
import AnalyticsDashboard from '@/components/AnalyticsDashboard';


interface feed_interface {
    _id: string;
    userId: string;
    role: string;
    feedbackType: string;
    comment?: string;
    createdAt: string;
    __v: number;
}

const AdminPage = () => {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [parsedData, setParsedData] = useState<any[]>([]);
    const [columns, setColumns] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);
    const [fetchSuccess, setfetchSuccess] = useState(false);
    const [classStd, setClassStd] = useState('');
    const [subject, setSubject] = useState('');
    const [subjectTeacher, setSubjectTeacher] = useState('');
    const [subjectTeacherEmail, setSubjectTeacherEmail] = useState('');
    const [credentials, setCredentials] = useState<any | null>(null);
    const [showCredentialsModal, setShowCredentialsModal] = useState(false);
    const [credentialsHistory, setCredentialsHistory] = useState<any[]>(() => {
        // Load from localStorage if available
        const saved = localStorage.getItem('credentialsHistory');
        return saved ? JSON.parse(saved) : [];
    });
    const [feedback, setFeedback] = useState<feed_interface[]>([]);
    const [showHistory, setShowHistory] = useState(false);
    const [activeTab, setActiveTab] = useState<'users' | 'analytics' | 'feedback'>('users');

    const axiosPrivate = useAxiosPrivate();

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSelectedFile(e.target.files?.[0] || null);
        setParsedData([]);
        setColumns([]);
    };

    // usePageRefresh();
    const fetchFeedback = async () => {
        if (feedback && feedback.length > 0) {
            return;
        } else {
            console.error("[INFO] fetching feedback from server...");
        }
        try {
            const response = await axiosPrivate.get('/api/feedback/feedbacks');
            console.log(`Fetched feedback: ${JSON.stringify(response.data)}`);
            setFeedback(response.data);
        } catch (error) {
            console.error(`Error fetching feedback: ${error}`);
        }
    }

    useEffect(() => {
        fetchFeedback();
    }, []);

    useEffect(() => {
        console.error(`[INFO] feedback changed: ${JSON.stringify(feedback)}`);
        setfetchSuccess(false);
    }, [feedback])

    const handleParseExcel = () => {
        if (!selectedFile) {
            toast.error('Select an Excel file.');
            return;
        }
        setLoading(true);
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const wb = XLSX.read(e.target?.result, { type: 'binary' });
                let allSheetsData: any[] = [];
                let headerColumns: string[] = [];

                if (!wb.SheetNames || wb.SheetNames.length === 0) {
                    console.error("Workbook contains no sheets.");
                }

                // Use headers from the first sheet as the standard for all.
                // If sheets can have different headers, you'll need a more complex merging strategy.
                const firstSheetName = wb.SheetNames[0];
                const firstWs = wb.Sheets[firstSheetName];
                if (!firstWs) {
                    console.error(`Sheet "${firstSheetName}" could not be read.`);
                }
                const firstSheetJsonData: any[] = XLSX.utils.sheet_to_json(firstWs, { header: 1, blankrows: false });

                if (firstSheetJsonData.length > 0 && Array.isArray(firstSheetJsonData[0]) && firstSheetJsonData[0].length > 0) {
                    headerColumns = firstSheetJsonData[0] as string[];
                } else {
                    // If the first sheet is empty or has no headers, we might not have columns for the table.
                    // You could try to find headers in subsequent sheets or throw an error.
                    // For this example, we'll allow it but log a warning.
                    console.warn("First sheet is empty or has no header row. Columns might be missing.");
                }
                setColumns(headerColumns);

                wb.SheetNames.forEach((sheetName, index) => {
                    const ws = wb.Sheets[sheetName];
                    if (!ws) {
                        console.warn(`Sheet "${sheetName}" could not be read. Skipping.`);
                        return; // Skip this sheet
                    }
                    // { header: 1 } gives an array of arrays
                    // { blankrows: false } helps in skipping empty rows if any
                    const sheetJsonData: any[] = XLSX.utils.sheet_to_json(ws, { header: 1, blankrows: false });

                    if (sheetJsonData.length > 0) {
                        let currentSheetHeader: string[];
                        let dataRows: any[];

                        // If it's the first sheet, its header is already taken by headerColumns.
                        // For subsequent sheets, we check if their header matches.
                        // This example assumes you want to merge data under the first sheet's header structure.
                        if (index === 0) {
                            currentSheetHeader = headerColumns; // Already extracted
                            dataRows = sheetJsonData.slice(1);
                        } else {
                            // For other sheets, we assume the first row is the header.
                            // You might want to add logic here if headers can differ and how to handle that.
                            // For simplicity, we're using the `headerColumns` from the first sheet for all.
                            currentSheetHeader = sheetJsonData[0] as string[]; // header of current sheet
                            dataRows = sheetJsonData.slice(1);
                        }

                        // If headerColumns is empty (e.g. first sheet was truly empty),
                        // try to get headers from the current sheet if it's the first one being effectively processed.
                        if (headerColumns.length === 0 && currentSheetHeader && currentSheetHeader.length > 0) {
                            headerColumns = currentSheetHeader;
                            setColumns(headerColumns); // Update columns if they were initially empty
                        }

                        if (headerColumns.length > 0) {
                            const processedRows = dataRows.map(rowArray => {
                                const rowObject: { [key: string]: any } = {};
                                headerColumns.forEach((colName, colIndex) => {
                                    rowObject[colName] = rowArray[colIndex] !== undefined ? rowArray[colIndex] : null; // Handle missing cells
                                });
                                return rowObject;
                            });
                            allSheetsData = allSheetsData.concat(processedRows);
                        } else if (dataRows.length > 0) {
                            // If no headers were ever found, but data exists, you might want to handle it differently
                            // e.g. treat data as raw arrays or try to infer headers.
                            // For now, we'll just log and potentially skip if no header structure is defined.
                            console.warn(`Sheet "${sheetName}" has data but no header structure defined from the first sheet. Rows might not be processed correctly.`);
                        }
                    }
                });

                if (allSheetsData.length === 0 && headerColumns.length === 0) {
                    console.error('No data or headers found in any sheet.');
                } else if (allSheetsData.length === 0 && headerColumns.length > 0) {
                    toast.info('Excel file parsed. Headers found, but no data rows across all sheets.');
                } else {
                    toast.success(`Parsed ${wb.SheetNames.length} sheet(s). Found ${allSheetsData.length} data rows.`);
                }
                setParsedData(allSheetsData);

            } catch (error: any) {
                toast.error(error.message || 'Invalid or empty Excel file.');
                setParsedData([]);
                setColumns([]);
            } finally {
                setLoading(false);
            }
        };
        reader.onerror = () => {
            toast.error('Error reading the file.');
            setLoading(false);
        };
        reader.readAsBinaryString(selectedFile);
    };

    // Save credentials to history and localStorage
    const saveCredentialsToHistory = (creds: any) => {
        const newHistory = [creds, ...credentialsHistory];
        setCredentialsHistory(newHistory);
        localStorage.setItem('credentialsHistory', JSON.stringify(newHistory));
    };

    const handleCreateUsers = async () => {
        if (!parsedData.length) {
            toast.error('No data to create users.');
            return;
        }
        if (!classStd || !subject) {
            toast.error('Please select both Class Standard and Subject.');
            return;
        }
        if (!subjectTeacher.trim()) {
            toast.error('Please enter the subject teacher name.');
            return;
        }
        if (!subjectTeacherEmail.trim()) {
            toast.error('Please enter the subject teacher email.');
            return;
        }
        setLoading(true);
        toast.info('Processing...');
        try {
            const response = await axiosPrivate.post('/api/admin/createUsers', { users: parsedData, classStd, subject, subjectTeacher, subjectTeacherEmail });
            if (response.status === 200) {
                toast.success('Users created successfully.');
                setParsedData([]);
                setColumns([]);
                setSelectedFile(null); // Clear the selected file
                // Reset file input visually (optional, depends on how you want the UX)
                const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
                if (fileInput) fileInput.value = '';

                setClassStd('');
                setSubject('');
                setSubjectTeacher('');
                setSubjectTeacherEmail('');
                setCredentials(response.data.credentials);
                setShowCredentialsModal(true);
                saveCredentialsToHistory(response.data.credentials); // Save to history
            }
            else {
                toast.error(response.data?.message || 'Failed to create users.');
                console.error('Error creating users:', response);
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Error creating users.');
            console.error('Error creating users:', error);
        } finally {
            setLoading(false);
        }
    };

    // PDF export function
    const exportCredentialsAsPDF = (creds: any) => {
        const doc = new jsPDF();
        let y = 10;
        doc.setFontSize(16);
        doc.text('Login Credentials', 10, y);
        y += 10;
        if (creds.teacher) {
            doc.setFontSize(12);
            doc.text('Subject Teacher:', 10, y);
            y += 8;
            doc.text(`Username: ${creds.teacher.username || ''}`, 12, y);
            y += 7;
            doc.text(`Email: ${creds.teacher.email || ''}`, 12, y);
            y += 7;
            doc.text(`Password: ${creds.teacher.password || ''}`, 12, y);
            y += 10;
        }
        if (creds.students && creds.students.length > 0) {
            doc.setFontSize(12);
            doc.text('Students:', 10, y);
            y += 8;
            doc.setFontSize(10);
            doc.text('First Name', 12, y);
            doc.text('Last Name', 52, y);
            doc.text('Username', 92, y);
            doc.text('Admission No.', 132, y);
            doc.text('Password', 172, y);
            y += 6;
            creds.students.forEach((stu: any) => {
                doc.text(String(stu.firstName || ''), 12, y);
                doc.text(String(stu.lastName || ''), 52, y);
                doc.text(String(stu.username || ''), 92, y);
                doc.text(String(stu.admissionNumber || ''), 132, y);
                doc.text(String(stu.password || ''), 172, y);
                y += 6;
                if (y > 270) { doc.addPage(); y = 10; }
            });
        }
        doc.save('credentials.pdf');
    };

    const theme = localStorage.getItem('theme') || 'light'; // Default to light theme if not set

    // JSX remains the same
    return (
        <div className="min-h-screen bg-background text-primary ">
            <ToastContainer position="top-right" autoClose={3000} hideProgressBar newestOnTop closeOnClick pauseOnFocusLoss draggable pauseOnHover theme={theme === 'dark' ? 'dark' : 'light'}/>
            <div className="container mx-auto px-4 py-8">
                <header className="mb-10 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-4xl font-bold text-primary">Admin Dashboard</h1>
                        <p className="text-lg text-muted-foreground mt-1">Manage platform operations and view analytics.</p>
                    </div>
                    <div className="flex justify-end items-center gap-3">
                        <ThemeToggle size="default" />
                        {activeTab === 'users' && (
                            <button
                                className="mt-4 sm:mt-0 bg-accent text-white px-4 py-2 rounded-md hover:bg-accent/90 transition"
                                onClick={() => setShowHistory(h => !h)}
                            >
                                {showHistory ? 'Hide History' : 'Show History'}
                            </button>
                        )}
                    </div>
                </header>

                {/* Tab Navigation */}
                <div className="mb-8">
                    <nav className="flex space-x-8 border-b border-border">
                        <button
                            onClick={() => setActiveTab('users')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                                activeTab === 'users'
                                    ? 'border-accent text-accent'
                                    : 'border-transparent text-muted-foreground hover:text-primary hover:border-border'
                            }`}
                        >
                            User Management
                        </button>
                        <button
                            onClick={() => setActiveTab('analytics')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                                activeTab === 'analytics'
                                    ? 'border-accent text-accent'
                                    : 'border-transparent text-muted-foreground hover:text-primary hover:border-border'
                            }`}
                        >
                            Analytics Dashboard
                        </button>
                        <button
                            onClick={() => setActiveTab('feedback')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                                activeTab === 'feedback'
                                    ? 'border-accent text-accent'
                                    : 'border-transparent text-muted-foreground hover:text-primary hover:border-border'
                            }`}
                        >
                            User Feedback
                        </button>
                    </nav>
                </div>
                {/* User Management Tab */}
                {activeTab === 'users' && (
                    <>
                {showHistory && (
                    <section className="bg-card p-6 rounded-lg shadow-md border border-border mb-8">
                        <h2 className="text-2xl font-semibold text-primary mb-4">Credentials History</h2>
                        {credentialsHistory.length === 0 ? (
                            <p className="text-muted-foreground">No credentials history found.</p>
                        ) : (
                            <div className="space-y-6 max-h-96 overflow-y-auto">
                                {credentialsHistory.map((creds, idx) => (
                                    <div key={idx} className="border border-border rounded p-4 bg-background">
                                        <div className="flex justify-between items-center mb-2">
                                            <span className="font-semibold text-accent">Entry #{credentialsHistory.length - idx}</span>
                                            <button
                                                className="bg-accent text-white px-3 py-1 rounded hover:bg-accent/90 text-xs"
                                                onClick={() => exportCredentialsAsPDF(creds)}
                                            >
                                                Export as PDF
                                            </button>
                                        </div>
                                        {creds.teacher && (
                                            <div className="mb-2">
                                                <span className="font-semibold">Teacher:</span> {creds.teacher.username} ({creds.teacher.email})
                                            </div>
                                        )}
                                        {creds.students && creds.students.length > 0 && (
                                            <div>
                                                <span className="font-semibold">Students:</span> {creds.students.length} students
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        )}
                    </section>
                )}
                <section className="bg-card p-6 rounded-lg shadow-md border border-border mb-8">
                    <h2 className="text-2xl font-semibold text-primary mb-4 flex items-center">
                        <UploadCloud className="h-6 w-6 mr-3 text-accent" />
                        1. Upload Excel File
                    </h2>
                    <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4">
                        <input
                            type="file"
                            accept=".xlsx, .xls"
                            onChange={handleFileChange}
                            className="block w-full text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-accent/10 file:text-accent hover:file:bg-accent/20 cursor-pointer focus:outline-none focus:ring-2 focus:ring-accent"
                        />
                        <button
                            onClick={handleParseExcel}
                            disabled={!selectedFile || loading}
                            className="w-full sm:w-auto bg-accent hover:bg-accent/90 text-white font-semibold py-2 px-6 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-background"
                        >
                            {loading ? 'Parsing...' : 'Parse Excel'}
                        </button>
                    </div>
                </section>
                {parsedData.length > 0 && columns.length > 0 && ( // Ensure columns are also present
                    <section className="bg-card p-6 rounded-lg shadow-md border border-border mb-8">
                        <h2 className="text-2xl font-semibold text-primary mb-4 flex items-center">
                            <FileText className="h-6 w-6 mr-3 text-accent" />
                            2. Review Data ({parsedData.length} rows from all sheets)
                        </h2>
                        <div className="overflow-x-auto rounded-md border border-border">
                            <table className="min-w-full divide-y divide-border">
                                <thead className="bg-accent/5">
                                    <tr>
                                        {columns.map((col, i) => (
                                            <th key={i} className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">{col}</th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody className="bg-card divide-y divide-border">
                                    {parsedData.map((row, i) => (
                                        <tr key={i} className={i % 2 === 0 ? 'bg-card' : 'bg-accent/5 hover:bg-accent/10'}>
                                            {columns.map((col, j) => (
                                                <td key={j} className="px-6 py-4 whitespace-nowrap text-sm text-primary">{String(row[col] ?? '')}</td>
                                            ))}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                        <p className="text-sm text-muted-foreground mt-4">Found {parsedData.length} data rows (excluding headers) across all sheets.</p>
                    </section>
                )}
                {parsedData.length > 0 && columns.length > 0 && ( // Ensure columns are also present
                    <section className="bg-card p-6 rounded-lg shadow-md border border-border">
                        <h2 className="text-2xl font-semibold text-primary mb-4 flex items-center">
                            <Users className="h-6 w-6 mr-3 text-accent" />
                            3. Create User Accounts
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                            <div>
                                <label className="block text-sm font-medium text-primary mb-1">Class Standard</label>
                                <select
                                    value={classStd}
                                    onChange={e => setClassStd(e.target.value)}
                                    className="block w-full border border-border rounded-md px-3 py-2 bg-background text-primary focus:outline-none focus:ring-2 focus:ring-accent"
                                >
                                    <option value="">Select Class</option>
                                    {[...Array(7)].map((_, i) => <option key={i + 6} value={String(i + 6)}>{i + 6}</option>)} {/* 6 to 12 */}
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-primary mb-1">Subject</label>
                                <select
                                    value={subject}
                                    onChange={e => setSubject(e.target.value)}
                                    className="block w-full border border-border rounded-md px-3 py-2 bg-background text-primary focus:outline-none focus:ring-2 focus:ring-accent"
                                >
                                    <option value="">Select Subject</option>
                                    <option value="Mathematics">Mathematics</option>
                                    <option value="Science">Science</option>
                                    <option value="English">English</option>
                                    <option value="Social Science">Social Science</option>
                                    <option value="Computer">Computer</option>
                                    {/* Add more subjects as needed */}
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-primary mb-1">Subject Teacher</label>
                                <input
                                    type="text"
                                    value={subjectTeacher}
                                    onChange={e => setSubjectTeacher(e.target.value)}
                                    placeholder="Enter subject teacher name"
                                    className="block w-full border border-border rounded-md px-3 py-2 bg-background text-primary focus:outline-none focus:ring-2 focus:ring-accent"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-primary mb-1">Subject Teacher Email</label>
                                <input
                                    type="email"
                                    value={subjectTeacherEmail}
                                    onChange={e => setSubjectTeacherEmail(e.target.value)}
                                    placeholder="Enter subject teacher email"
                                    className="block w-full border border-border rounded-md px-3 py-2 bg-background text-primary focus:outline-none focus:ring-2 focus:ring-accent"
                                />
                            </div>
                        </div>
                        <button
                            onClick={handleCreateUsers}
                            disabled={loading || !parsedData.length}
                            className="w-full sm:w-auto bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-6 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-background"
                        >
                            {loading ? 'Processing...' : `Create ${parsedData.length} Users`}
                        </button>
                    </section>
                )}

                {showCredentialsModal && credentials && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm">
                        <div className="bg-card border border-border rounded-xl shadow-xl p-6 w-full max-w-2xl relative">
                            <button
                                className="absolute top-3 right-3 text-muted-foreground hover:text-primary p-1"
                                onClick={() => setShowCredentialsModal(false)}
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                            </button>
                            <h2 className="text-2xl font-bold mb-4 text-primary">Login Credentials</h2>
                            {credentials.teacher && (
                                <div className="mb-6">
                                    <h3 className="text-lg font-semibold mb-2 text-accent">Subject Teacher</h3>
                                    <table className="w-full mb-4 border border-border rounded text-sm">
                                        <thead className="bg-accent/10">
                                            <tr>
                                                <th className="px-4 py-2 text-left font-medium">Username</th>
                                                <th className="px-4 py-2 text-left font-medium">Email</th>
                                                <th className="px-4 py-2 text-left font-medium">Password</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td className="px-4 py-2">{credentials.teacher.username}</td>
                                                <td className="px-4 py-2">{credentials.teacher.email}</td>
                                                <td className="px-4 py-2 font-mono">{credentials.teacher.password}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            )}
                            {credentials.students && credentials.students.length > 0 && (
                                <div>
                                    <h3 className="text-lg font-semibold mb-2 text-accent">Students</h3>
                                    <div className="overflow-y-auto max-h-72 border border-border rounded">
                                        <table className="w-full text-sm">
                                            <thead className="bg-accent/10 sticky top-0 backdrop-blur-xl">
                                                <tr>
                                                    <th className="px-3 py-2 text-left font-medium">First Name</th>
                                                    <th className="px-3 py-2 text-left font-medium">Last Name</th>
                                                    <th className="px-3 py-2 text-left font-medium">Username</th>
                                                    <th className="px-3 py-2 text-left font-medium">Admission No.</th>
                                                    <th className="px-3 py-2 text-left font-medium">Password</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {credentials.students.map((stu: any, idx: number) => (
                                                    <tr key={idx} className={`${idx % 2 === 0 ? 'bg-card' : 'bg-accent/5'} hover:bg-accent/10`}>
                                                        <td className="px-3 py-2">{stu.firstName}</td>
                                                        <td className="px-3 py-2">{stu.lastName}</td>
                                                        <td className="px-3 py-2">{stu.username}</td>
                                                        <td className="px-3 py-2">{stu.admissionNumber}</td>
                                                        <td className="px-3 py-2 font-mono">{stu.password}</td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            )}
                            <button
                                onClick={() => setShowCredentialsModal(false)}
                                className="mt-6 w-full bg-accent hover:bg-accent/90 text-white font-semibold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                            >
                                Close
                            </button>
                            <button
                                className="absolute top-3 right-14 text-accent hover:text-primary p-1"
                                title="Export as PDF"
                                onClick={() => exportCredentialsAsPDF(credentials)}
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 5v14M19 12l-7 7-7-7" /></svg>
                            </button>
                        </div>
                    </div>
                )}
                    </>
                )}

                {/* Analytics Dashboard Tab */}
                {activeTab === 'analytics' && (
                    <AnalyticsDashboard />
                )}

                {/* User Feedback Tab */}
                {activeTab === 'feedback' && (
                    <div className="max-w-4xl mx-auto p-6">
                        <h2 className="text-3xl font-bold text-primary mb-8">User Feedback</h2>
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-1">
                            {feedback.map((item) => (
                                <FeedbackCard key={item._id} {...item} />
                            ))}
                        </div>
                        {feedback.length === 0 && (
                            <div className="text-center py-12">
                                <p className="text-muted-foreground">No feedback available.</p>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default AdminPage;