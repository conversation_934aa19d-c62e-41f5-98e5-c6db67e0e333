import { UsersIcon, CalendarIcon, AcademicCapIcon, BookOpenIcon, Squares2X2Icon } from '@heroicons/react/24/outline';
import Background from '../components/Background';
import { useUser } from '../contexts/userContext';
import TaskList from '../components/TaskList';
import ClassList from '../components/ClassList';
import ViewUpcomingTest from '../components/ViewUpcomingTest';
import TeacherTestHistory from '../components/TeacherTestHistory';
import AegisScholarLogoWithoutText from '@/assets/AegisScholarLogoIcon';
import FeedBackComp from '@/components/Feedback';

const TeacherDashboard = () => {
  const { user, setUser } = useUser();
  const totalStudents = user?.classes?.reduce((acc, curr) => acc + curr.students.length, 0);



  return (
    <div className="w-full flex flex-col gap-4 lg:h-screen lg:overflow-hidden min-h-screen overflow-x-hidden">
      {/* <Background /> */}

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto flex flex-col gap-4 p-4 pb-6 mb-12">
        {/* Header - Fixed height */}
        <div className="flex items-center gap-3 p-2">
          <div className='flex flex-row items-start'>
            <AegisScholarLogoWithoutText
              className="w-12 h-12 lg:hidden mr-2"
              style={{ fill: 'var(--color-accent)' }}
            />
            <div className="flex flex-col">
            <small className="text-sm sm:text-base font-[Space_Grotesk] text-muted-foreground">Hello</small>
            <h1 className="text-xl lg:text-2xl font-[Space_Grotesk] font-bold text-primary">{user?.username}!</h1>
            </div>
          </div>
        </div>

        {/* Main Content - Reordered for mobile */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-4 min-h-0">
          {/* Classes Section - Moves below stats on mobile */}
          <div className="order-1 lg:order-2 lg:col-span-3 flex flex-col">
            <div className="flex-1 overflow-auto">
              <ClassList />
            </div>
          </div>

          {/* Left Column - Moves below classes on mobile */}
          <div className="order-2 lg:order-1 col-span-1 flex flex-col gap-4 min-h-0">
            {/* Scheduled Tests Section */}
            <div className="overflow-hidden h-[300px] md:h-[350px] lg:h-full lg:border-r lg:pr-4">
              <ViewUpcomingTest />
            </div>

            {/* Test History Section */}
            <div className="overflow-hidden h-[300px] md:h-[350px] lg:h-full lg:border-r lg:pr-4">
              <TeacherTestHistory />
            </div>
          </div>
        </div>
      </div>
      <FeedBackComp id={user?.id} role={user?.role}/>
    </div>
  );

};

export default TeacherDashboard;
