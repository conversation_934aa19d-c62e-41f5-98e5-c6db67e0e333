#!/usr/bin/env node

/**
 * Test script to verify collection targeting fixes
 * Tests without requiring database connection
 */

import textToQueryService from '../services/textToQueryService.js';

// Test collection targeting logic
function testCollectionTargeting() {
  console.log('🎯 TESTING COLLECTION TARGETING FIXES');
  console.log('=====================================');
  
  // Test 1: Performance Analysis should not include aegisGrader
  console.log('\n📊 Test 1: Performance Analysis Collections');
  const perfAnalysisNormal = textToQueryService.determineTargetCollections('PERFORMANCE_ANALYSIS', 'normal');
  const perfAnalysisDeep = textToQueryService.determineTargetCollections('PERFORMANCE_ANALYSIS', 'deep');
  
  console.log(`Normal mode: [${perfAnalysisNormal.join(', ')}]`);
  console.log(`Deep mode: [${perfAnalysisDeep.join(', ')}]`);
  
  if (!perfAnalysisNormal.includes('aegisGrader') && !perfAnalysisDeep.includes('aegisGrader')) {
    console.log('✅ aegisGrader successfully removed from PERFORMANCE_ANALYSIS');
  } else {
    console.log('❌ aegisGrader still present in PERFORMANCE_ANALYSIS');
  }
  
  // Test 2: Class Overview should include teachers
  console.log('\n👥 Test 2: Class Overview Collections');
  const classOverviewNormal = textToQueryService.determineTargetCollections('CLASS_OVERVIEW', 'normal');
  const classOverviewDeep = textToQueryService.determineTargetCollections('CLASS_OVERVIEW', 'deep');
  
  console.log(`Normal mode: [${classOverviewNormal.join(', ')}]`);
  console.log(`Deep mode: [${classOverviewDeep.join(', ')}]`);
  
  if (classOverviewNormal.includes('teachers') || classOverviewDeep.includes('teachers')) {
    console.log('✅ teachers collection added to CLASS_OVERVIEW');
  } else {
    console.log('❌ teachers collection missing from CLASS_OVERVIEW');
  }
  
  // Test 3: Knowledge Gaps should not include aegisGrader
  console.log('\n🧠 Test 3: Knowledge Gaps Collections');
  const knowledgeGapsNormal = textToQueryService.determineTargetCollections('KNOWLEDGE_GAPS', 'normal');
  const knowledgeGapsDeep = textToQueryService.determineTargetCollections('KNOWLEDGE_GAPS', 'deep');
  
  console.log(`Normal mode: [${knowledgeGapsNormal.join(', ')}]`);
  console.log(`Deep mode: [${knowledgeGapsDeep.join(', ')}]`);
  
  if (!knowledgeGapsNormal.includes('aegisGrader') && !knowledgeGapsDeep.includes('aegisGrader')) {
    console.log('✅ aegisGrader successfully removed from KNOWLEDGE_GAPS');
  } else {
    console.log('❌ aegisGrader still present in KNOWLEDGE_GAPS');
  }
  
  // Test 4: Test Analysis should not include aegisGrader
  console.log('\n📝 Test 4: Test Analysis Collections');
  const testAnalysisNormal = textToQueryService.determineTargetCollections('TEST_ANALYSIS', 'normal');
  const testAnalysisDeep = textToQueryService.determineTargetCollections('TEST_ANALYSIS', 'deep');
  
  console.log(`Normal mode: [${testAnalysisNormal.join(', ')}]`);
  console.log(`Deep mode: [${testAnalysisDeep.join(', ')}]`);
  
  if (!testAnalysisNormal.includes('aegisGrader') && !testAnalysisDeep.includes('aegisGrader')) {
    console.log('✅ aegisGrader successfully removed from TEST_ANALYSIS');
  } else {
    console.log('❌ aegisGrader still present in TEST_ANALYSIS');
  }
  
  // Test 5: Teacher Analytics should include teachers
  console.log('\n👨‍🏫 Test 5: Teacher Analytics Collections');
  const teacherAnalyticsNormal = textToQueryService.determineTargetCollections('TEACHER_ANALYTICS', 'normal');
  const teacherAnalyticsDeep = textToQueryService.determineTargetCollections('TEACHER_ANALYTICS', 'deep');
  
  console.log(`Normal mode: [${teacherAnalyticsNormal.join(', ')}]`);
  console.log(`Deep mode: [${teacherAnalyticsDeep.join(', ')}]`);
  
  if (teacherAnalyticsNormal.includes('teachers') && teacherAnalyticsDeep.includes('teachers')) {
    console.log('✅ teachers collection properly included in TEACHER_ANALYTICS');
  } else {
    console.log('❌ teachers collection missing from TEACHER_ANALYTICS');
  }
}

// Test access controls
function testAccessControls() {
  console.log('\n🔒 TESTING ACCESS CONTROLS');
  console.log('=====================================');
  
  // Test student access controls
  console.log('\n👨‍🎓 Student Access Controls:');
  const studentCollections = ['students', 'testHistory', 'teachers', 'chatConversations'];
  const studentFiltered = textToQueryService.applyAccessControls(studentCollections, 'student');
  console.log(`Original: [${studentCollections.join(', ')}]`);
  console.log(`Filtered: [${studentFiltered.join(', ')}]`);
  
  if (!studentFiltered.includes('teachers')) {
    console.log('✅ Students correctly blocked from teachers collection');
  } else {
    console.log('❌ Students can access teachers collection (should be blocked)');
  }
  
  // Test teacher access controls
  console.log('\n👨‍🏫 Teacher Access Controls:');
  const teacherCollections = ['students', 'testHistory', 'teachers', 'chatConversations'];
  const teacherFiltered = textToQueryService.applyAccessControls(teacherCollections, 'teacher');
  console.log(`Original: [${teacherCollections.join(', ')}]`);
  console.log(`Filtered: [${teacherFiltered.join(', ')}]`);
  
  if (!teacherFiltered.includes('chatConversations')) {
    console.log('✅ Teachers correctly blocked from chatConversations');
  } else {
    console.log('❌ Teachers can access chatConversations (should be blocked)');
  }
  
  if (teacherFiltered.includes('teachers')) {
    console.log('✅ Teachers can access teachers collection');
  } else {
    console.log('❌ Teachers blocked from teachers collection (should be allowed)');
  }
}

// Test collection schemas
function testCollectionSchemas() {
  console.log('\n📋 TESTING COLLECTION SCHEMAS');
  console.log('=====================================');
  
  const schemas = textToQueryService.initializeCollectionSchemas();
  
  // Check if aegisGrader is removed
  if (!schemas.hasOwnProperty('aegisGrader')) {
    console.log('✅ aegisGrader schema removed');
  } else {
    console.log('❌ aegisGrader schema still present');
  }
  
  // Check if teachers schema is added
  if (schemas.hasOwnProperty('teachers')) {
    console.log('✅ teachers schema added');
    console.log(`   Fields: [${schemas.teachers.fields.join(', ')}]`);
  } else {
    console.log('❌ teachers schema missing');
  }
  
  // Check enhanced testHistory schema
  if (schemas.testHistory.fields.includes('studentId') && schemas.testHistory.fields.includes('score')) {
    console.log('✅ testHistory schema enhanced with additional fields');
  } else {
    console.log('❌ testHistory schema not properly enhanced');
  }
}

// Run all tests
function runAllTests() {
  try {
    testCollectionTargeting();
    testAccessControls();
    testCollectionSchemas();
    
    console.log('\n🎉 SUMMARY');
    console.log('=====================================');
    console.log('✅ Collection targeting fixes verified');
    console.log('✅ Access control updates verified');
    console.log('✅ Schema updates verified');
    console.log('✅ aegisGrader dependency successfully removed');
    console.log('✅ Teacher model integration successfully added');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

runAllTests();
