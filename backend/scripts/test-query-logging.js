#!/usr/bin/env node

/**
 * Test Script for Query Logging System
 * 
 * This script tests the comprehensive logging system for the text-to-MongoDB query pipeline
 * to identify what data is being retrieved and analyze current limitations.
 */

import mongoose from 'mongoose';
import { config } from 'dotenv';
import textToQueryService from '../services/textToQueryService.js';
import queryExecutionEngine from '../services/queryExecutionEngine.js';
import contextInjectionService from '../services/contextInjectionService.js';

config();

// Test scenarios to analyze different types of queries
const testScenarios = [
  {
    name: "Test Analysis Request",
    query: "Can you analyze my recent test performance in detail?",
    userType: "student",
    userId: "test_student_123",
    subject: "Mathematics",
    conversationHistory: []
  },
  {
    name: "Learning Progress Query",
    query: "How am I progressing in my mathematics curriculum?",
    userType: "student", 
    userId: "test_student_123",
    subject: "Mathematics",
    conversationHistory: []
  },
  {
    name: "Knowledge Gaps Identification",
    query: "What topics do I need to work on more?",
    userType: "student",
    userId: "test_student_123", 
    subject: "Mathematics",
    conversationHistory: []
  },
  {
    name: "Teacher Class Overview",
    query: "How is my class performing overall?",
    userType: "teacher",
    userId: "teacher_456",
    subject: "Mathematics",
    conversationHistory: []
  }
];

async function connectToDatabase() {
  try {
    // Use the correct environment variable based on NODE_ENV
    const mongoUri = process.env.NODE_ENV === 'production'
      ? process.env.MONGO_URI_PROD
      : process.env.MONGO_URI_TEST;

    if (!mongoUri) {
      throw new Error('MongoDB URI not found in environment variables');
    }

    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
    console.log(`📍 Environment: ${process.env.NODE_ENV || 'development'}`);
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

async function testQueryScenario(scenario) {
  console.log('\n' + '='.repeat(80));
  console.log(`🧪 TESTING SCENARIO: ${scenario.name}`);
  console.log('='.repeat(80));
  console.log(`Query: "${scenario.query}"`);
  console.log(`User: ${scenario.userType}:${scenario.userId}`);
  console.log(`Subject: ${scenario.subject}`);
  console.log('');

  try {
    // Step 1: Test Query Generation
    console.log('📝 STEP 1: QUERY GENERATION');
    console.log('-'.repeat(40));
    
    const queryGeneration = await textToQueryService.generateQuery(
      scenario.query,
      scenario.userType,
      scenario.userId,
      scenario.subject,
      'normal',
      scenario.conversationHistory
    );

    if (!queryGeneration.success) {
      console.log('❌ Query generation failed');
      console.log('Reason:', queryGeneration.reason);
      console.log('Fallback required:', queryGeneration.fallbackToTraditional);
      return;
    }

    console.log('✅ Query generation successful');
    console.log(`Generated ${queryGeneration.queries.length} queries for intent: ${queryGeneration.intent}`);

    // Step 2: Test Query Execution
    console.log('\n📊 STEP 2: QUERY EXECUTION');
    console.log('-'.repeat(40));

    const queryExecution = await queryExecutionEngine.executeQueries(
      queryGeneration.queries,
      scenario.userType,
      scenario.userId,
      scenario.subject
    );

    if (!queryExecution.success) {
      console.log('❌ Query execution failed');
      console.log('Error:', queryExecution.error);
      return;
    }

    console.log('✅ Query execution successful');
    
    // Step 3: Test Context Generation
    console.log('\n🔄 STEP 3: CONTEXT GENERATION');
    console.log('-'.repeat(40));

    const contextGeneration = await contextInjectionService.generateEnhancedContext(
      queryExecution.results,
      scenario.query,
      scenario.userType,
      scenario.subject,
      scenario.conversationHistory
    );

    if (!contextGeneration.success) {
      console.log('❌ Context generation failed');
      console.log('Error:', contextGeneration.error);
      return;
    }

    console.log('✅ Context generation successful');

    // Step 4: Analysis and Recommendations
    console.log('\n📈 STEP 4: DATA ANALYSIS & RECOMMENDATIONS');
    console.log('-'.repeat(40));

    analyzeDataRetrieval(queryExecution, contextGeneration, scenario);

  } catch (error) {
    console.error('❌ Test scenario failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

function analyzeDataRetrieval(queryExecution, contextGeneration, scenario) {
  console.log('📊 DATA RETRIEVAL ANALYSIS:');
  
  // Analyze what data was actually retrieved
  const summary = queryExecution.summary;
  console.log(`  • Total queries executed: ${summary.totalQueries}`);
  console.log(`  • Successful queries: ${summary.successfulQueries}`);
  console.log(`  • Total documents retrieved: ${summary.totalDocuments}`);
  console.log(`  • Collections accessed: [${summary.collectionsAccessed.join(', ')}]`);
  console.log(`  • Execution time: ${summary.executionTime}ms`);

  // Analyze data quality and completeness
  console.log('\n🔍 DATA QUALITY ANALYSIS:');
  
  let hasDetailedTestData = false;
  let hasProgressData = false;
  let hasGraderAnalysis = false;
  let totalDataPoints = 0;

  queryExecution.results.forEach(result => {
    if (result.success && result.data) {
      const docCount = Array.isArray(result.data) ? result.data.length : 1;
      totalDataPoints += docCount;
      
      console.log(`  • ${result.collection}: ${docCount} documents`);
      
      // Check for specific data types
      if (result.collection === 'testHistory' && docCount > 0) {
        hasDetailedTestData = true;
      }
      if (result.collection === 'studentKnowledgeGraph' && docCount > 0) {
        hasProgressData = true;
      }
      if (result.collection === 'aegisGrader' && docCount > 0) {
        hasGraderAnalysis = true;
      }
    }
  });

  // Provide recommendations based on analysis
  console.log('\n💡 RECOMMENDATIONS FOR IMPROVEMENT:');
  
  if (totalDataPoints === 0) {
    console.log('  ❌ CRITICAL: No data retrieved - check database connectivity and user permissions');
  } else if (totalDataPoints < 5) {
    console.log('  ⚠️  WARNING: Limited data retrieved - may result in shallow responses');
  } else {
    console.log('  ✅ GOOD: Sufficient data retrieved for meaningful analysis');
  }

  if (scenario.query.toLowerCase().includes('test') && !hasDetailedTestData) {
    console.log('  ❌ MISSING: Test analysis requested but no test history data retrieved');
  }

  if (scenario.query.toLowerCase().includes('progress') && !hasProgressData) {
    console.log('  ❌ MISSING: Progress analysis requested but no knowledge graph data retrieved');
  }

  if (scenario.query.toLowerCase().includes('detail') && !hasGraderAnalysis) {
    console.log('  ⚠️  MISSING: Detailed analysis requested but no grader analysis data retrieved');
  }

  // Context analysis
  const contextMeta = contextGeneration.metadata;
  console.log('\n🎯 CONTEXT GENERATION ANALYSIS:');
  console.log(`  • Original results: ${contextMeta.originalResultsCount}`);
  console.log(`  • Relevant results: ${contextMeta.relevantResultsCount}`);
  console.log(`  • Collections processed: [${contextMeta.collectionsProcessed.join(', ')}]`);
  console.log(`  • Context size: ${contextMeta.contextSize} characters`);
  console.log(`  • Insights generated: ${contextMeta.insightsGenerated || 0}`);

  if (contextMeta.relevantResultsCount < contextMeta.originalResultsCount) {
    console.log(`  ⚠️  ${contextMeta.originalResultsCount - contextMeta.relevantResultsCount} results filtered out as irrelevant`);
  }
}

async function runAllTests() {
  console.log('🚀 STARTING COMPREHENSIVE QUERY LOGGING TESTS');
  console.log('='.repeat(80));
  
  await connectToDatabase();
  
  for (const scenario of testScenarios) {
    await testQueryScenario(scenario);
    
    // Add delay between tests to avoid overwhelming the system
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('✅ ALL TESTS COMPLETED');
  console.log('='.repeat(80));
  
  await mongoose.disconnect();
  console.log('📤 Disconnected from MongoDB');
}

// Run the tests
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  });
}

export { runAllTests, testQueryScenario };
