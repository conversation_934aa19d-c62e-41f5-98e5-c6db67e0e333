import { GoogleGenerativeAI } from '@google/generative-ai';
import { config } from 'dotenv';
import crypto from 'crypto';

config();

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const queryGenerationModel = "gemini-2.0-flash";

/**
 * Text-to-MongoDB Query Generation Service
 * Converts natural language queries into structured MongoDB queries
 * with privacy controls and collection-specific optimizations
 */
class TextToQueryService {
  constructor() {
    this.collectionSchemas = this.initializeCollectionSchemas();
    this.queryTemplates = this.initializeQueryTemplates();
    this.privacyRules = this.initializePrivacyRules();
  }

  /**
   * Main method to convert natural language to MongoDB query
   * @param {string} naturalLanguageQuery - User's natural language query
   * @param {string} userType - 'student' or 'teacher'
   * @param {string} userId - User ID for access control
   * @param {string} subject - Subject context
   * @param {string} analysisMode - 'normal' or 'deep'
   * @param {Array} conversationHistory - Previous conversation context
   * @returns {Promise<Object>} Generated MongoDB query structure
   */
  async generateQuery(naturalLanguageQuery, userType, userId, subject, analysisMode = 'normal', conversationHistory = []) {
    try {
      console.log(`[TEXT-TO-QUERY] ========== STARTING QUERY GENERATION ==========`);
      console.log(`[TEXT-TO-QUERY] Input parameters:`);
      console.log(`  - Query: "${naturalLanguageQuery}"`);
      console.log(`  - User: ${userType}:${userId}`);
      console.log(`  - Subject: ${subject}`);
      console.log(`  - Analysis Mode: ${analysisMode}`);
      console.log(`  - Conversation History: ${conversationHistory.length} messages`);

      // Check if query needs conversation context
      console.log(`[TEXT-TO-QUERY] Step 1: Analyzing conversation context...`);
      const contextAnalysis = this.analyzeConversationContext(naturalLanguageQuery, conversationHistory);
      console.log(`[TEXT-TO-QUERY] Context analysis result:`, {
        needsContext: contextAnalysis.needsContext,
        reason: contextAnalysis.reason,
        confidence: contextAnalysis.confidence
      });

      if (contextAnalysis.needsContext) {
        console.log(`[TEXT-TO-QUERY] Query "${naturalLanguageQuery}" requires conversation context (${contextAnalysis.reason}), delegating to traditional method`);
        return {
          success: false,
          reason: 'REQUIRES_CONVERSATION_CONTEXT',
          contextAnalysis,
          fallbackToTraditional: true,
          message: 'Query requires conversation context and will be handled by traditional method'
        };
      }

      // Step 1: Classify the query intent
      console.log(`[TEXT-TO-QUERY] Step 2: Classifying query intent...`);
      const queryIntent = await this.classifyQueryIntent(naturalLanguageQuery, subject);
      console.log(`[TEXT-TO-QUERY] Classified intent: ${queryIntent}`);

      // Step 2: Determine target collections based on intent
      console.log(`[TEXT-TO-QUERY] Step 3: Determining target collections...`);
      const targetCollections = this.determineTargetCollections(queryIntent, analysisMode);
      console.log(`[TEXT-TO-QUERY] Target collections for ${queryIntent} (${analysisMode} mode): [${targetCollections.join(', ')}]`);

      // Step 3: Apply privacy and access controls
      console.log(`[TEXT-TO-QUERY] Step 4: Applying access controls...`);
      const allowedCollections = this.applyAccessControls(targetCollections, userType);
      console.log(`[TEXT-TO-QUERY] Allowed collections for ${userType}: [${allowedCollections.join(', ')}]`);

      if (allowedCollections.length === 0) {
        console.warn(`[TEXT-TO-QUERY] No collections allowed for user type ${userType}`);
        return {
          success: false,
          reason: 'NO_ALLOWED_COLLECTIONS',
          fallbackToTraditional: true,
          message: 'No collections accessible for this user type'
        };
      }

      // Step 4: Generate structured MongoDB queries
      console.log(`[TEXT-TO-QUERY] Step 5: Generating MongoDB queries...`);
      const mongoQueries = await this.generateMongoQueries(
        naturalLanguageQuery,
        queryIntent,
        allowedCollections,
        userId,
        subject,
        analysisMode,
        conversationHistory
      );
      console.log(`[TEXT-TO-QUERY] Generated ${mongoQueries.length} MongoDB queries`);

      // Step 5: Validate and optimize queries
      console.log(`[TEXT-TO-QUERY] Step 6: Validating and optimizing queries...`);
      const validatedQueries = this.validateAndOptimizeQueries(mongoQueries);
      console.log(`[TEXT-TO-QUERY] Validated ${validatedQueries.length} queries`);

      // Log each validated query
      validatedQueries.forEach((query, index) => {
        console.log(`[TEXT-TO-QUERY] Query ${index + 1}:`);
        console.log(`  - Collection: ${query.collection}`);
        console.log(`  - Operation: ${query.operation}`);
        console.log(`  - Limit: ${query.limit}`);
        console.log(`  - Has projection: ${!!query.projection}`);
        console.log(`  - Has optimization hints: ${!!query.optimizationHints}`);
      });

      console.log(`[TEXT-TO-QUERY] ========== QUERY GENERATION COMPLETED ==========`);

      return {
        success: true,
        intent: queryIntent,
        queries: validatedQueries,
        analysisMode,
        targetCollections: allowedCollections,
        metadata: {
          generatedAt: new Date(),
          userId,
          userType,
          subject,
          contextAnalysis,
          originalTargetCollections: targetCollections,
          finalCollections: allowedCollections
        }
      };
      
    } catch (error) {
      console.error('[TEXT-TO-QUERY] Error generating query:', error);
      return {
        success: false,
        error: error.message,
        fallbackToTraditional: true
      };
    }
  }

  /**
   * Analyze if query needs conversation context and provide detailed reasoning
   */
  analyzeConversationContext(query, conversationHistory) {
    const trimmedQuery = query.trim().toLowerCase();
    const hasConversationHistory = conversationHistory && conversationHistory.length > 0;

    // Short responses that likely refer to previous context
    const contextualResponses = [
      'yes', 'no', 'okay', 'ok', 'sure', 'thanks', 'thank you',
      'continue', 'more', 'next', 'previous', 'again', 'repeat',
      'explain', 'clarify', 'elaborate', 'details', 'how', 'why',
      'what about', 'tell me more', 'go on', 'and then', 'also'
    ];

    // If we have conversation history, be more lenient with short queries
    if (hasConversationHistory) {
      console.log(`[TEXT-TO-QUERY] Conversation history available (${conversationHistory.length} messages), allowing context-aware processing`);

      // Only delegate very ambiguous queries when we have conversation history
      if (trimmedQuery.length < 2) {
        return {
          needsContext: true,
          reason: 'EXTREMELY_SHORT_QUERY',
          details: `Query "${query}" is extremely short (${trimmedQuery.length} characters) and ambiguous even with conversation context`,
          confidence: 0.95
        };
      }

      // Allow most queries to be processed when we have conversation history
      // The LLM can use the conversation context to understand the intent
      return {
        needsContext: false,
        reason: 'HAS_CONVERSATION_CONTEXT',
        details: `Query "${query}" can be processed using available conversation context (${conversationHistory.length} messages)`,
        confidence: 0.8
      };
    }

    // No conversation history - use stricter analysis
    console.log('[TEXT-TO-QUERY] No conversation history available, using strict context analysis');

    // Check if query is too short (less than 4 characters) when no conversation history
    if (trimmedQuery.length < 4) {
      return {
        needsContext: true,
        reason: 'QUERY_TOO_SHORT',
        details: `Query "${query}" is too short (${trimmedQuery.length} characters) to be meaningful without conversation context`,
        confidence: 0.9
      };
    }

    // Check if query matches contextual response patterns (only when no conversation history)
    const matchedResponse = contextualResponses.find(response => {
      // Exact match
      if (trimmedQuery === response) {
        return true;
      }

      // For question words (how, what, why, etc.), be more strict
      if (['how', 'what', 'why', 'when', 'where', 'which'].includes(response)) {
        // Only match if it's a very short query (less than 4 words) or ends with just the question word
        const words = trimmedQuery.split(/\s+/);
        return words.length <= 3 && trimmedQuery.startsWith(response + ' ');
      }

      // For other contextual responses, use the original logic
      return trimmedQuery.startsWith(response + ' ');
    });

    if (matchedResponse) {
      return {
        needsContext: true,
        reason: 'CONTEXTUAL_RESPONSE',
        details: `Query "${query}" appears to be a contextual response (matched: "${matchedResponse}") and no conversation history is available`,
        confidence: 0.95
      };
    }

    // Check if query contains pronouns that likely refer to previous context (only when no conversation history)
    const pronouns = ['it', 'this', 'that', 'these', 'those', 'they', 'them'];
    const foundPronoun = pronouns.find(pronoun =>
      trimmedQuery.includes(' ' + pronoun + ' ') || trimmedQuery.startsWith(pronoun + ' ')
    );
    if (foundPronoun) {
      return {
        needsContext: true,
        reason: 'CONTAINS_PRONOUNS',
        details: `Query "${query}" contains pronoun "${foundPronoun}" that likely refers to previous context and no conversation history is available`,
        confidence: 0.8
      };
    }

    // Check for incomplete questions or statements (only when no conversation history)
    const incompletePatterns = [
      /^(and|but|so|then|also|however|moreover|furthermore)/,
      // Very short question words without meaningful content
      /^(what|how|why|when|where|which)\s*(about|if|when)?\s*\??\s*$/,
      // Questions that are just question words with minimal content (less than 3 words after question word)
      /^(what|how|why|when|where|which)\s+\w{1,3}\s*\??\s*$/
    ];

    const matchedPattern = incompletePatterns.find(pattern => pattern.test(trimmedQuery));
    if (matchedPattern) {
      // Additional check: if the query has substantial content (more than 6 words), it's likely complete
      const wordCount = trimmedQuery.split(/\s+/).length;
      if (wordCount > 6) {
        // This is likely a complete question despite matching the pattern
        return {
          needsContext: false,
          reason: 'SELF_CONTAINED',
          details: `Query "${query}" has substantial content (${wordCount} words) and appears complete`,
          confidence: 0.8
        };
      }

      return {
        needsContext: true,
        reason: 'INCOMPLETE_QUERY',
        details: `Query "${query}" appears incomplete and no conversation history is available to provide context`,
        confidence: 0.7
      };
    }

    return {
      needsContext: false,
      reason: 'SELF_CONTAINED',
      details: `Query "${query}" appears to be self-contained and can be processed independently`,
      confidence: 0.8
    };
  }

  /**
   * Check if query needs conversation context to be meaningful (legacy method)
   * @deprecated Use analyzeConversationContext for more detailed analysis
   */
  needsConversationContext(query, conversationHistory) {
    const analysis = this.analyzeConversationContext(query, conversationHistory);
    return analysis.needsContext;
  }

  /**
   * Classify the intent of the natural language query
   */
  async classifyQueryIntent(query, subject) {
    const classificationPrompt = `
You are a query intent classifier for an educational AI system. Analyze the following student/teacher query and classify it into one of these categories:

INTENT CATEGORIES:
1. PERFORMANCE_ANALYSIS - Questions about test scores, grades, performance trends
2. LEARNING_PROGRESS - Questions about curriculum progress, completed topics, learning path
3. KNOWLEDGE_GAPS - Questions about weaknesses, areas needing improvement, struggling topics
4. STRENGTHS_IDENTIFICATION - Questions about strong areas, mastered topics, achievements
5. CONTENT_RETRIEVAL - Questions about specific questions, topics, or educational content
6. COMPARATIVE_ANALYSIS - Questions comparing performance across topics, tests, or time periods
7. RECOMMENDATION_REQUEST - Questions asking for study suggestions, next steps, or guidance
8. CLASS_OVERVIEW - Teacher queries about overall class performance (teacher-specific)

QUERY: "${query}"
SUBJECT: "${subject}"

Respond with ONLY the intent category name (e.g., "PERFORMANCE_ANALYSIS").
`;

    try {
      const model = genAI.getGenerativeModel({ model: queryGenerationModel });
      const result = await model.generateContent(classificationPrompt);
      const response = await result.response;
      const intent = response.text().trim().toUpperCase();
      
      // Validate intent
      const validIntents = [
        'PERFORMANCE_ANALYSIS', 'LEARNING_PROGRESS', 'KNOWLEDGE_GAPS',
        'STRENGTHS_IDENTIFICATION', 'CONTENT_RETRIEVAL', 'COMPARATIVE_ANALYSIS',
        'RECOMMENDATION_REQUEST', 'CLASS_OVERVIEW'
      ];
      
      return validIntents.includes(intent) ? intent : 'GENERAL_INQUIRY';
    } catch (error) {
      console.error('[TEXT-TO-QUERY] Intent classification error:', error);
      return 'GENERAL_INQUIRY';
    }
  }

  /**
   * Determine which collections to target based on query intent
   */
  determineTargetCollections(intent, analysisMode) {
    const collectionMap = {
      'PERFORMANCE_ANALYSIS': ['testHistory', 'aegisGrader', 'studentKnowledgeGraph'],
      'LEARNING_PROGRESS': ['studentKnowledgeGraph', 'students', 'curriculumNodes'],
      'KNOWLEDGE_GAPS': ['testHistory', 'aegisGrader', 'studentKnowledgeGraph'],
      'STRENGTHS_IDENTIFICATION': ['testHistory', 'aegisGrader', 'studentKnowledgeGraph'],
      'CONTENT_RETRIEVAL': ['questions', 'curriculumNodes', 'aiQuestions'],
      'COMPARATIVE_ANALYSIS': ['testHistory', 'aegisGrader', 'studentKnowledgeGraph'],
      'RECOMMENDATION_REQUEST': ['studentKnowledgeGraph', 'testHistory', 'curriculumNodes'],
      'CLASS_OVERVIEW': ['students', 'testHistory', 'aegisGrader', 'studentKnowledgeGraph'],
      'GENERAL_INQUIRY': ['students', 'studentKnowledgeGraph']
    };

    let collections = collectionMap[intent] || collectionMap['GENERAL_INQUIRY'];
    
    // Limit collections for normal analysis mode
    if (analysisMode === 'normal') {
      collections = collections.slice(0, 2);
    }
    
    return collections;
  }

  /**
   * Apply access controls based on user type
   */
  applyAccessControls(collections, userType) {
    if (userType === 'teacher') {
      // Teachers cannot access chat conversations
      return collections.filter(collection => collection !== 'chatConversations');
    }
    
    return collections;
  }

  /**
   * Generate actual MongoDB queries using LLM
   */
  async generateMongoQueries(query, intent, collections, userId, subject, analysisMode, conversationHistory = []) {
    // Format conversation history for the prompt
    const conversationContext = conversationHistory.length > 0
      ? `\nCONVERSATION HISTORY:\n${conversationHistory.map((msg, idx) =>
          `${idx + 1}. ${msg.role}: ${msg.content}`
        ).join('\n')}\n`
      : '\nCONVERSATION HISTORY: None\n';

    const queryGenerationPrompt = `
You are a MongoDB query generator for an educational platform. Generate MongoDB queries based on the natural language query.

CONTEXT:
- User Query: "${query}"
- Intent: ${intent}
- Target Collections: ${collections.join(', ')}
- Subject: ${subject}
- Analysis Mode: ${analysisMode}
- User ID: ${userId}
${conversationContext}

IMPORTANT: If the user query is short or contextual (like "ok", "yes", "more", "explain"), use the conversation history above to understand what the user is referring to. Generate queries that address the context from previous messages.

COLLECTION SCHEMAS:
${JSON.stringify(this.collectionSchemas, null, 2)}

PRIVACY RULES:
- Never include sensitive fields like email, password, deviceInfo
- Always filter by userId for student data
- Use projection to limit returned fields
- For teachers, aggregate class data without individual student details

QUERY REQUIREMENTS:
1. Generate efficient MongoDB queries with proper filtering
2. Include appropriate projections to limit data
3. Use aggregation pipelines for complex analysis
4. Ensure queries respect privacy and access controls
5. Optimize for performance with proper indexing

Generate queries in this JSON format:
{
  "queries": [
    {
      "collection": "collectionName",
      "operation": "find|aggregate",
      "query": {...},
      "projection": {...},
      "sort": {...},
      "limit": number,
      "pipeline": [...] // for aggregation
    }
  ]
}

IMPORTANT: Respond with ONLY valid JSON. Do NOT use markdown code blocks or any formatting. Return raw JSON only.
`;

    let queryText = '';

    try {
      // Check if API key is available
      if (!process.env.GEMINI_API_KEY) {
        throw new Error('GEMINI_API_KEY not found in environment variables');
      }

      const model = genAI.getGenerativeModel({
        model: queryGenerationModel,
        generationConfig: {
          temperature: 0.1,
          topP: 0.8,
          maxOutputTokens: 2048,
          candidateCount: 1
        }
      });

      console.log('[TEXT-TO-QUERY] Sending request to Gemini API...');
      console.log('[TEXT-TO-QUERY] Prompt length:', queryGenerationPrompt.length);
      console.log('[TEXT-TO-QUERY] Using model:', queryGenerationModel);

      const result = await model.generateContent(queryGenerationPrompt);
      console.log('[TEXT-TO-QUERY] Received result from API');

      const response = await result.response;
      console.log('[TEXT-TO-QUERY] Extracted response object');

      // Check if response is valid
      if (!response) {
        throw new Error('No response received from Gemini API');
      }

      queryText = response.text().trim();

      console.log('[TEXT-TO-QUERY] Raw LLM response received, length:', queryText.length);
      console.log('[TEXT-TO-QUERY] First 200 chars:', queryText.substring(0, 200));

      // Check if response is empty
      if (!queryText || queryText.length === 0) {
        throw new Error('Empty response received from Gemini API');
      }

      // Extract JSON from markdown code blocks if present - robust extraction
      if (queryText.includes('```')) {
        // Try multiple patterns to extract JSON
        const patterns = [
          /```json\s*([\s\S]*?)\s*```/,  // ```json ... ```
          /```javascript\s*([\s\S]*?)\s*```/, // ```javascript ... ```
          /```\s*([\s\S]*?)\s*```/,      // ``` ... ```
        ];

        for (const pattern of patterns) {
          const match = queryText.match(pattern);
          if (match) {
            queryText = match[1].trim();
            break;
          }
        }
      }

      // Additional cleanup - remove any remaining backticks or markdown
      queryText = queryText.replace(/^`+|`+$/g, '').trim();

      // If still starts with non-JSON characters, try to find JSON object
      if (!queryText.startsWith('{')) {
        const jsonMatch = queryText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          queryText = jsonMatch[0];
        }
      }

      console.log('[TEXT-TO-QUERY] Cleaned text for parsing, length:', queryText.length);
      console.log('[TEXT-TO-QUERY] Cleaned text first 200 chars:', queryText.substring(0, 200));

      // Parse JSON response
      const parsedQueries = JSON.parse(queryText);
      console.log('[TEXT-TO-QUERY] Successfully parsed JSON, found', parsedQueries.queries?.length || 0, 'queries');
      return parsedQueries.queries || [];

    } catch (error) {
      console.error('[TEXT-TO-QUERY] Query generation error:', error);
      console.error('[TEXT-TO-QUERY] Raw LLM response length:', queryText?.length || 0);
      console.error('[TEXT-TO-QUERY] Raw LLM response:', queryText);

      // Try to extract JSON from malformed response as last resort
      if (queryText && typeof queryText === 'string') {
        try {
          // Look for JSON-like structure in the response
          const jsonMatch = queryText.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const extractedJson = JSON.parse(jsonMatch[0]);
            console.log('[TEXT-TO-QUERY] Successfully extracted JSON from malformed response');
            return extractedJson.queries || [];
          }
        } catch (extractError) {
          console.error('[TEXT-TO-QUERY] Failed to extract JSON from malformed response:', extractError);
        }
      }

      throw new Error('Failed to generate MongoDB queries');
    }
  }

  /**
   * Validate and optimize generated queries
   */
  validateAndOptimizeQueries(queries) {
    return queries.map(query => {
      // Add safety limits
      if (!query.limit || query.limit > 100) {
        query.limit = 50; // Default safety limit
      }
      
      // Ensure proper projection for privacy
      if (!query.projection) {
        query.projection = this.getDefaultProjection(query.collection);
      }
      
      // Add query optimization hints
      query.optimizationHints = {
        useIndex: true,
        maxTimeMS: 5000
      };
      
      return query;
    });
  }

  /**
   * Get default projection for privacy protection
   */
  getDefaultProjection(collection) {
    const projections = {
      'students': {
        'password': 0,
        'securityToken': 0,
        'email': 0,
        'subjects.attemptedTests.metadata': 0
      },
      'testHistory': {
        'questions': 0 // Exclude full question content for performance
      },
      'aegisGrader': {
        'answerSheets.metadata': 0
      },
      'chatConversations': {
        'messages.metadata': 0
      }
    };
    
    return projections[collection] || {};
  }

  /**
   * Initialize collection schemas for query generation
   */
  initializeCollectionSchemas() {
    return {
      students: {
        fields: ['username', 'firstName', 'lastName', 'admissionNumber', 'subjects', 'classes'],
        relationships: ['subjects.subjectClassId', 'classes'],
        indexes: ['username', 'admissionNumber', 'subjects.subjectName']
      },
      studentKnowledgeGraph: {
        fields: ['studentId', 'subject', 'curriculumProgress', 'overallProficiency'],
        relationships: ['studentId'],
        indexes: ['studentId', 'subject']
      },
      testHistory: {
        fields: ['class', 'subject', 'testType', 'topics', 'testDate', 'numberOfQuestions', 'totalMarks'],
        relationships: ['class'],
        indexes: ['subject', 'testDate', 'class']
      },
      aegisGrader: {
        fields: ['testId', 'subject', 'overallScore', 'questionAnalysis', 'gradedAt'],
        relationships: ['testId'],
        indexes: ['subject', 'gradedAt', 'testId']
      },
      questions: {
        fields: ['question', 'topic', 'subtopic', 'difficulty', 'answer'],
        relationships: [],
        indexes: ['topic', 'subtopic', 'difficulty']
      }
    };
  }

  /**
   * Initialize query templates for common patterns
   */
  initializeQueryTemplates() {
    return {
      studentPerformance: {
        collection: 'testHistory',
        operation: 'aggregate',
        pipeline: [
          { $match: { subject: '$subject' } },
          { $group: { _id: '$testType', avgScore: { $avg: '$totalMarks' } } }
        ]
      },
      learningProgress: {
        collection: 'studentKnowledgeGraph',
        operation: 'find',
        query: { studentId: '$userId', subject: '$subject' },
        projection: { curriculumProgress: 1, overallProficiency: 1 }
      }
    };
  }

  /**
   * Initialize privacy rules
   */
  initializePrivacyRules() {
    return {
      excludeFields: [
        'password', 'securityToken', 'email', 'metadata.ipAddress',
        'metadata.device', 'metadata.browser'
      ],
      teacherRestrictions: [
        'chatConversations'
      ],
      dataLimits: {
        normal: { maxDocuments: 50, maxCollections: 2 },
        deep: { maxDocuments: 200, maxCollections: 5 }
      }
    };
  }
}

export default new TextToQueryService();
