import { GoogleGenerativeAI } from '@google/generative-ai';
import textToQueryService from './textToQueryService.js';
import queryExecutionEngine from './queryExecutionEngine.js';
import contextInjectionService from './contextInjectionService.js';
import queryCacheService from './queryCacheService.js';
import LLMCache from '../models/LLMCacheModel.js';
import ChatConversation from '../models/ChatConversation.js';
import { config } from 'dotenv';
import crypto from 'crypto';

config();

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const defaultGeminiModel = "gemini-2.0-flash";

/**
 * Enhanced Teaching Assistant Service
 * Uses the new text-to-MongoDB query pipeline for optimized data retrieval
 */
class EnhancedTeachingAssistantService {
  constructor() {
    this.fallbackToTraditional = false;
  }

  /**
   * Enhanced chatbot response with text-to-MongoDB query system
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getChatbotResponseWithQuerySystem(req, res) {
    const startTime = Date.now();
    
    try {
      const { studentId, subject, message, conversationHistory = [], conversationId, userName } = req.body;

      if (!studentId || !subject || !message) {
        return res.status(400).json({
          message: "Missing required fields: studentId, subject, and message are required.",
          error: "Bad Request"
        });
      }

      console.log(`[ENHANCED-TA] Processing query-based chat request for: ${studentId}, subject: ${subject}`);

      // Determine user type (student vs teacher)
      const userType = this.determineUserType(studentId);
      const actualUserId = this.extractActualUserId(studentId, userType);

      // Step 1: Generate MongoDB queries from natural language with conversation context
      const queryGeneration = await textToQueryService.generateQuery(
        message,
        userType,
        actualUserId,
        subject,
        'normal', // analysisMode
        conversationHistory
      );

      if (!queryGeneration.success) {
        if (queryGeneration.reason === 'REQUIRES_CONVERSATION_CONTEXT') {
          console.log(`[ENHANCED-TA] Query requires conversation context (${queryGeneration.contextAnalysis?.reason}), using traditional method`);
        } else {
          console.warn('[ENHANCED-TA] Query generation failed, falling back to traditional method');
        }
        return this.fallbackToTraditionalMethod(req, res);
      }

      // Step 2: Check query cache first
      const cachedQueryResults = await queryCacheService.checkQueryCache(
        message,
        actualUserId,
        subject,
        queryGeneration.queries
      );

      if (cachedQueryResults) {
        console.log(`[QUERY-CACHE HIT] Returning cached query results for user ${actualUserId}`);

        // Generate context from cached results
        const contextGeneration = await contextInjectionService.generateEnhancedContext(
          cachedQueryResults.results,
          message,
          userType,
          subject,
          conversationHistory
        );

        // Generate response with cached data
        const enhancedPrompt = this.buildEnhancedPrompt(
          contextGeneration.success ? contextGeneration.context : contextGeneration.fallbackContext,
          message,
          userName || 'there',
          userType
        );

        const responseText = await this.callGeminiAPIForChat(enhancedPrompt);

        // Save conversation and return response
        await this.saveConversationMessage(conversationId, studentId, subject, message, responseText);

        return res.json({
          message: responseText,
          studentContext: {
            knowledgeLevel: userType === 'teacher' ? 'teacher_multi_student' : 'student_individual',
            dataSource: 'cached_query_results',
            queriesExecuted: cachedQueryResults.metadata?.queriesExecuted || 0,
            collectionsAccessed: cachedQueryResults.metadata?.collectionsAccessed || [],
            documentsRetrieved: cachedQueryResults.metadata?.documentsRetrieved || 0,
            responseTime: Date.now() - startTime,
            cached: true
          }
        });
      }

      // Step 3: Execute the generated queries
      const queryExecution = await queryExecutionEngine.executeQueries(
        queryGeneration.queries,
        userType,
        actualUserId,
        subject
      );

      if (!queryExecution.success) {
        console.warn('[ENHANCED-TA] Query execution failed, falling back to traditional method');
        return this.fallbackToTraditionalMethod(req, res);
      }

      // Step 4: Generate enhanced context from query results
      const contextGeneration = await contextInjectionService.generateEnhancedContext(
        queryExecution.results,
        message,
        userType,
        subject,
        conversationHistory
      );

      if (!contextGeneration.success) {
        console.warn('[ENHANCED-TA] Context generation failed, using fallback context');
      }

      // Step 5: Store query results in cache for future use
      await queryCacheService.storeQueryCache(
        message,
        actualUserId,
        subject,
        queryGeneration.queries,
        queryExecution,
        contextGeneration,
        {
          userType,
          responseTime: Date.now() - startTime
        }
      );

      // Step 4: Generate the enhanced prompt
      const enhancedPrompt = this.buildEnhancedPrompt(
        contextGeneration.success ? contextGeneration.context : contextGeneration.fallbackContext,
        message,
        userName || 'there',
        userType
      );

      // Step 5: Call Gemini API
      let responseText;
      try {
        responseText = await this.callGeminiAPIForChat(enhancedPrompt);
      } catch (apiError) {
        console.error('[ENHANCED-TA] Gemini API error:', apiError.message);
        responseText = "I'm currently experiencing high demand. Please try your question again in a moment, and I'll provide you with personalized insights about your learning progress.";
      }

      // Step 6: Save conversation
      await this.saveConversationMessage(conversationId, studentId, subject, message, responseText);

      // Step 7: Cache the response
      await this.updateChatCache(
        studentId,
        subject,
        conversationId,
        message,
        responseText,
        {
          querySystemUsed: true,
          queriesGenerated: queryGeneration.queries?.length || 0,
          collectionsAccessed: queryExecution.summary?.collectionsAccessed || [],
          contextSize: contextGeneration.metadata?.contextSize || 0,
          responseTime: Date.now() - startTime
        }
      );

      // Step 8: Build response
      const chatbotResponse = {
        message: responseText,
        studentContext: {
          knowledgeLevel: userType === 'teacher' ? 'teacher_multi_student' : 'student_individual',
          dataSource: 'query_based_retrieval',
          queriesExecuted: queryExecution.summary?.totalQueries || 0,
          collectionsAccessed: queryExecution.summary?.collectionsAccessed || [],
          documentsRetrieved: queryExecution.summary?.totalDocuments || 0,
          responseTime: Date.now() - startTime
        }
      };

      console.log(`[ENHANCED-TA] Query-based response generated successfully in ${Date.now() - startTime}ms`);
      res.json(chatbotResponse);

    } catch (error) {
      console.error('Error in enhanced teaching assistant controller:', error);
      res.status(500).json({
        message: "I'm having trouble processing your request right now. Please try again in a moment.",
        error: error.message
      });
    }
  }

  /**
   * Determine if user is student or teacher based on studentId pattern
   */
  determineUserType(studentId) {
    return studentId.startsWith('teacher_') ? 'teacher' : 'student';
  }

  /**
   * Extract actual user ID from the studentId parameter
   */
  extractActualUserId(studentId, userType) {
    if (userType === 'teacher') {
      // Extract teacher ID from pattern: teacher_<teacherId>_class_<classId>
      const parts = studentId.split('_');
      return parts[1]; // teacherId
    }
    return studentId;
  }

  /**
   * Build enhanced prompt with query-based context
   */
  buildEnhancedPrompt(enhancedContext, userMessage, userName, userType) {
    const roleDescription = userType === 'teacher' 
      ? 'AegisAI for Teachers, an advanced AI teaching assistant'
      : 'AegisAI, your personalized AI learning companion';

    const contextSection = enhancedContext.dataContext && Object.keys(enhancedContext.dataContext).length > 0
      ? `
RETRIEVED DATA CONTEXT:
${this.formatDataContextForLLM(enhancedContext.dataContext, userType)}

CONTEXTUAL INSIGHTS:
${enhancedContext.contextualInsights?.map((insight, i) => `${i + 1}. ${insight}`).join('\n') || 'No specific insights available'}
`
      : 'Limited context available - providing general educational guidance.';

    return `You are ${roleDescription} with access to targeted educational data retrieved specifically for this query.

${contextSection}

CONVERSATION HISTORY:
${enhancedContext.conversationHistory?.map(msg => `${msg.type}: ${msg.content}`).join('\n') || 'No previous conversation'}

CURRENT QUESTION: ${userMessage}

PRIVACY AND RESPONSE GUIDELINES (CRITICAL):
**Privacy Protection:**
- NEVER reveal private information: email addresses, phone numbers, real names, addresses, personal identifiers
- NEVER display raw database IDs, ObjectIds, or internal system identifiers
- NEVER expose technical metadata: device info, browser details, IP addresses, session tokens, system config
- Always use resolved meaningful content (topic names, question text) instead of raw nodeids or question IDs

**Response Style:**
- Address the user as "${userName}" in a friendly, supportive manner
- Provide concise, focused answers that directly address their question
- Use the retrieved data context to give personalized insights
- Avoid lengthy explanations unless specifically requested
- If data is limited, acknowledge this and provide general educational guidance
- For teachers: Focus on class-wide insights and actionable recommendations
- For students: Focus on personal learning progress and next steps

**Educational Focus:**
- Prioritize actionable insights over raw data presentation
- Suggest specific next steps or improvements when relevant
- Encourage continued learning and growth
- Maintain a positive, motivating tone

Please provide a helpful, personalized response based on the available context.`;
  }

  /**
   * Format data context in a human-readable way for LLM
   */
  formatDataContextForLLM(dataContext, userType) {
    let formattedContext = '';

    for (const [collection, data] of Object.entries(dataContext)) {
      formattedContext += `\n${collection.toUpperCase()} DATA:\n`;
      formattedContext += `Summary: ${data.summary}\n`;

      if (data.keyInsights && data.keyInsights.length > 0) {
        formattedContext += `Key Insights:\n`;
        data.keyInsights.forEach((insight) => {
          formattedContext += `  • ${insight}\n`;
        });
      }

      if (data.rawData && data.rawData.length > 0) {
        formattedContext += `Details:\n`;
        if (collection === 'students' && userType === 'teacher') {
          data.rawData.forEach((student, i) => {
            formattedContext += `  Student ${i + 1}: ${student.name || 'Name not available'}\n`;
            if (student.subjects && student.subjects.length > 0) {
              formattedContext += `    Subjects: ${student.subjects.join(', ')}\n`;
            }
          });
        } else {
          // For other collections, show limited structured data
          formattedContext += `  ${data.dataCount} records available\n`;
        }
      }
      formattedContext += '\n';
    }

    return formattedContext.trim();
  }

  /**
   * Call Gemini API for chat response
   */
  async callGeminiAPIForChat(prompt, modelName = defaultGeminiModel) {
    try {
      const model = genAI.getGenerativeModel({ model: modelName });
      const result = await model.generateContent(prompt);
      const response = await result.response;
      let text = response.text();

      // Clean up the response text
      text = text.trim();

      return text;
    } catch (error) {
      console.error(`Error calling Gemini API for chat with model ${modelName}:`, error);
      throw error;
    }
  }

  /**
   * Check cache for existing response
   */
  async checkChatCache(studentId, subject, conversationId, message) {
    try {
      const cacheKey = LLMCache.generateCacheKey(studentId, subject, 'chat', conversationId, message);
      const cachedEntry = await LLMCache.findOne({ cacheKey });

      if (cachedEntry) {
        await cachedEntry.incrementHitCount();
        return cachedEntry;
      }

      return null;
    } catch (error) {
      console.error('Error checking chat cache:', error);
      return null;
    }
  }

  /**
   * Update cache with new response
   */
  async updateChatCache(studentId, subject, conversationId, message, response, metadata = {}) {
    try {
      const cacheKey = LLMCache.generateCacheKey(studentId, subject, 'chat', conversationId, message);
      const messageHash = LLMCache.generateMessageHash(message);

      const cacheEntry = new LLMCache({
        studentId,
        subject,
        requestType: 'chat',
        conversationId,
        messageHash,
        cacheKey,
        response: { message: response },
        metadata: {
          ...metadata,
          modelUsed: defaultGeminiModel,
          mcpEnabled: false,
          querySystemEnabled: true,
          dataSource: 'query_based_retrieval'
        }
      });

      await cacheEntry.save();
      console.log(`[CACHE] Cached response for key: ${cacheKey}`);
    } catch (error) {
      console.error('Error updating chat cache:', error);
    }
  }

  /**
   * Save conversation message
   */
  async saveConversationMessage(conversationId, userId, subject, userMessage, aiResponse) {
    try {
      if (!conversationId) return;

      const conversation = await ChatConversation.findOne({ id: conversationId });
      
      if (conversation) {
        conversation.messages.push(
          {
            id: `msg_${Date.now()}_user`,
            type: 'user',
            content: userMessage,
            timestamp: new Date()
          },
          {
            id: `msg_${Date.now() + 1}_assistant`,
            type: 'assistant',
            content: aiResponse,
            timestamp: new Date()
          }
        );
        conversation.updatedAt = new Date();
        await conversation.save();
      }
    } catch (error) {
      console.error('Error saving conversation message:', error);
    }
  }

  /**
   * Fallback to traditional mongoMcpService method
   */
  async fallbackToTraditionalMethod(req, res) {
    try {
      console.log('[ENHANCED-TA] Using traditional fallback method');
      // Import the traditional service dynamically to avoid circular dependencies
      const { getChatbotResponseWithMCP } = await import('./teachingAssistantService.js');
      return getChatbotResponseWithMCP(req, res);
    } catch (error) {
      console.error('[ENHANCED-TA] Fallback method also failed:', error);
      return res.status(500).json({
        message: "I'm experiencing technical difficulties. Please try again in a moment.",
        error: "Service temporarily unavailable"
      });
    }
  }

  /**
   * Health check method to verify all services are working
   */
  async healthCheck() {
    try {
      const testQuery = "How am I performing in Mathematics?";
      const testGeneration = await textToQueryService.generateQuery(
        testQuery,
        'student',
        'test_user',
        'Mathematics'
      );

      return {
        status: 'healthy',
        services: {
          textToQuery: testGeneration.success,
          queryExecution: true, // Basic check
          contextInjection: true, // Basic check
          geminiAPI: true // Would need actual test
        },
        timestamp: new Date()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }
}

export default new EnhancedTeachingAssistantService();
