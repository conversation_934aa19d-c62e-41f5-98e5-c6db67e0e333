import mongoose from 'mongoose';
import Student from '../models/Student.js';
import { StudentCurriculumModel } from '../models/StudentKnowledgeGraphModel.js';
import TestHistory from '../models/TestHistory.js';
import AegisGrader from '../models/AegisGrader.js';
import ChatConversation from '../models/ChatConversation.js';
import Class from '../models/Class.js';
import Teacher from '../models/Teacher.js';
import { createKnowledgeGraphModel } from '../models/knowledgeGraphModel.js';
import { createQuestionModel } from '../models/Question.js';
import AIQuestion from '../models/AIQuestions.js';
import QuestionTemp from '../models/QuestionTemp.js';
import privacySecurityService from './privacySecurityService.js';

/**
 * Query Execution Engine
 * Securely executes MongoDB queries with privacy controls and data sanitization
 */
class QueryExecutionEngine {
  constructor() {
    this.modelMap = this.initializeModelMap();
    this.privacyFilters = this.initializePrivacyFilters();
    this.accessControlMatrix = this.initializeAccessControlMatrix();
  }

  /**
   * Execute a batch of MongoDB queries with security and privacy controls
   * @param {Array} queries - Array of query objects from textToQueryService
   * @param {string} userType - 'student' or 'teacher'
   * @param {string} userId - User ID for access control
   * @param {string} subject - Subject context
   * @returns {Promise<Object>} Execution results with sanitized data
   */
  async executeQueries(queries, userType, userId, subject) {
    try {
      console.log(`[QUERY-EXECUTION] ========== STARTING QUERY EXECUTION ==========`);
      console.log(`[QUERY-EXECUTION] User: ${userType}:${userId}, Subject: ${subject}`);
      console.log(`[QUERY-EXECUTION] Total queries to execute: ${queries.length}`);

      // Log each query before execution
      queries.forEach((query, index) => {
        console.log(`[QUERY-EXECUTION] Query ${index + 1}:`);
        console.log(`  - Collection: ${query.collection}`);
        console.log(`  - Operation: ${query.operation}`);
        console.log(`  - Query: ${JSON.stringify(query.query || {}, null, 2)}`);
        console.log(`  - Projection: ${JSON.stringify(query.projection || {}, null, 2)}`);
        console.log(`  - Limit: ${query.limit || 'none'}`);
        if (query.pipeline) {
          console.log(`  - Pipeline: ${JSON.stringify(query.pipeline, null, 2)}`);
        }
      });

      const results = [];
      const executionMetadata = {
        startTime: Date.now(),
        queriesExecuted: 0,
        queriesSuccessful: 0,
        totalDocuments: 0,
        collectionsAccessed: [],
        detailedResults: []
      };

      for (const [queryIndex, query] of queries.entries()) {
        const queryStartTime = Date.now();
        console.log(`[QUERY-EXECUTION] ========== EXECUTING QUERY ${queryIndex + 1}/${queries.length} ==========`);
        console.log(`[QUERY-EXECUTION] Collection: ${query.collection}`);

        try {
          // Step 1: Validate and sanitize query with privacy controls
          console.log(`[QUERY-EXECUTION] Step 1: Validating and sanitizing query...`);
          const secureQuery = privacySecurityService.validateAndSanitizeQuery(
            query,
            userType,
            userId,
            query.collection
          );
          console.log(`[QUERY-EXECUTION] Secure query created: ${JSON.stringify(secureQuery.query || {}, null, 2)}`);

          // Step 2: Apply additional user-specific filters
          console.log(`[QUERY-EXECUTION] Step 2: Applying user-specific filters...`);
          const enhancedQuery = await this.applyUserFilters(secureQuery, userType, userId, subject);
          console.log(`[QUERY-EXECUTION] Enhanced query: ${JSON.stringify(enhancedQuery.query || {}, null, 2)}`);
          console.log(`[QUERY-EXECUTION] Enhanced projection: ${JSON.stringify(enhancedQuery.projection || {}, null, 2)}`);

          // Step 3: Execute the query
          console.log(`[QUERY-EXECUTION] Step 3: Executing query against database...`);
          const queryResult = await this.executeSecureQuery(enhancedQuery);
          const rawResultCount = Array.isArray(queryResult) ? queryResult.length : (queryResult ? 1 : 0);
          console.log(`[QUERY-EXECUTION] Raw query result: ${rawResultCount} document(s) retrieved`);

          // Log sample of raw data (first document only, truncated)
          if (queryResult && rawResultCount > 0) {
            const sampleDoc = Array.isArray(queryResult) ? queryResult[0] : queryResult;
            const sampleKeys = Object.keys(sampleDoc || {});
            console.log(`[QUERY-EXECUTION] Sample document keys: [${sampleKeys.slice(0, 10).join(', ')}${sampleKeys.length > 10 ? '...' : ''}]`);

            // Log specific important fields based on collection
            this.logImportantFields(query.collection, sampleDoc);
          } else {
            console.log(`[QUERY-EXECUTION] No data retrieved from ${query.collection}`);
          }

          // Step 4: Sanitize results with privacy service
          console.log(`[QUERY-EXECUTION] Step 4: Sanitizing results...`);
          const sanitizedResult = privacySecurityService.sanitizeQueryResults(
            queryResult,
            query.collection,
            userType
          );
          const sanitizedResultCount = Array.isArray(sanitizedResult) ? sanitizedResult.length : (sanitizedResult ? 1 : 0);
          console.log(`[QUERY-EXECUTION] Sanitized result: ${sanitizedResultCount} document(s) after sanitization`);

          const queryExecutionTime = Date.now() - queryStartTime;
          const resultData = {
            collection: query.collection,
            operation: query.operation,
            success: true,
            data: sanitizedResult,
            documentCount: sanitizedResultCount,
            executionTime: queryExecutionTime
          };

          results.push(resultData);

          // Detailed logging for metadata
          const detailedResult = {
            queryIndex: queryIndex + 1,
            collection: query.collection,
            operation: query.operation,
            originalQuery: query.query || {},
            enhancedQuery: enhancedQuery.query || {},
            projection: enhancedQuery.projection || {},
            rawDocumentCount: rawResultCount,
            sanitizedDocumentCount: sanitizedResultCount,
            executionTimeMs: queryExecutionTime,
            success: true
          };
          executionMetadata.detailedResults.push(detailedResult);

          executionMetadata.queriesSuccessful++;
          executionMetadata.totalDocuments += sanitizedResultCount;

          if (!executionMetadata.collectionsAccessed.includes(query.collection)) {
            executionMetadata.collectionsAccessed.push(query.collection);
          }

          console.log(`[QUERY-EXECUTION] Query ${queryIndex + 1} completed successfully in ${queryExecutionTime}ms`);

        } catch (queryError) {
          const queryExecutionTime = Date.now() - queryStartTime;
          console.error(`[QUERY-EXECUTION] Query ${queryIndex + 1} failed for collection ${query.collection}:`, queryError);
          console.error(`[QUERY-EXECUTION] Error details:`, {
            message: queryError.message,
            stack: queryError.stack?.split('\n').slice(0, 3).join('\n')
          });

          const errorResult = {
            collection: query.collection,
            operation: query.operation,
            success: false,
            error: 'Query execution failed',
            data: null,
            executionTime: queryExecutionTime
          };
          results.push(errorResult);

          // Detailed error logging
          const detailedResult = {
            queryIndex: queryIndex + 1,
            collection: query.collection,
            operation: query.operation,
            originalQuery: query.query || {},
            error: queryError.message,
            executionTimeMs: queryExecutionTime,
            success: false
          };
          executionMetadata.detailedResults.push(detailedResult);
        }

        executionMetadata.queriesExecuted++;
      }

      executionMetadata.endTime = Date.now();
      executionMetadata.totalExecutionTime = executionMetadata.endTime - executionMetadata.startTime;

      // Final comprehensive logging
      console.log(`[QUERY-EXECUTION] ========== EXECUTION SUMMARY ==========`);
      console.log(`[QUERY-EXECUTION] Total execution time: ${executionMetadata.totalExecutionTime}ms`);
      console.log(`[QUERY-EXECUTION] Queries executed: ${executionMetadata.queriesExecuted}/${queries.length}`);
      console.log(`[QUERY-EXECUTION] Successful queries: ${executionMetadata.queriesSuccessful}`);
      console.log(`[QUERY-EXECUTION] Total documents retrieved: ${executionMetadata.totalDocuments}`);
      console.log(`[QUERY-EXECUTION] Collections accessed: [${executionMetadata.collectionsAccessed.join(', ')}]`);

      // Log detailed breakdown by collection
      const collectionSummary = {};
      executionMetadata.detailedResults.forEach(result => {
        if (!collectionSummary[result.collection]) {
          collectionSummary[result.collection] = {
            queries: 0,
            successful: 0,
            totalDocs: 0,
            avgExecutionTime: 0
          };
        }
        collectionSummary[result.collection].queries++;
        if (result.success) {
          collectionSummary[result.collection].successful++;
          collectionSummary[result.collection].totalDocs += result.sanitizedDocumentCount || 0;
        }
        collectionSummary[result.collection].avgExecutionTime += result.executionTimeMs;
      });

      Object.entries(collectionSummary).forEach(([collection, stats]) => {
        stats.avgExecutionTime = Math.round(stats.avgExecutionTime / stats.queries);
        console.log(`[QUERY-EXECUTION] ${collection}: ${stats.successful}/${stats.queries} successful, ${stats.totalDocs} docs, avg ${stats.avgExecutionTime}ms`);
      });

      console.log(`[QUERY-EXECUTION] ========== END EXECUTION SUMMARY ==========`);

      return {
        success: true,
        results,
        metadata: {
          ...executionMetadata,
          collectionSummary
        },
        summary: {
          totalQueries: queries.length,
          successfulQueries: executionMetadata.queriesSuccessful,
          totalDocuments: executionMetadata.totalDocuments,
          collectionsAccessed: executionMetadata.collectionsAccessed,
          executionTime: executionMetadata.totalExecutionTime,
          collectionBreakdown: collectionSummary
        }
      };

    } catch (error) {
      console.error('[QUERY-EXECUTION] Batch execution failed:', error);
      return {
        success: false,
        error: error.message,
        results: [],
        fallbackToTraditional: true
      };
    }
  }

  /**
   * Log important fields from retrieved documents for debugging
   */
  logImportantFields(collection, document) {
    if (!document) return;

    try {
      switch (collection) {
        case 'students':
          console.log(`[QUERY-EXECUTION] Student data: name=${document.firstName} ${document.lastName}, subjects=${document.subjects?.length || 0}, classes=${document.classes?.length || 0}`);
          break;

        case 'studentKnowledgeGraph':
          console.log(`[QUERY-EXECUTION] Knowledge graph: studentId=${document.studentId}, subject=${document.subject}, proficiency=${document.overallProficiency}%, progress=${document.curriculumProgress?.length || 0} topics`);
          break;

        case 'testHistory':
          console.log(`[QUERY-EXECUTION] Test history: subject=${document.subject}, type=${document.testType}, date=${document.testDate}, questions=${document.numberOfQuestions}, marks=${document.totalMarks}`);
          break;

        case 'aegisGrader':
          console.log(`[QUERY-EXECUTION] Grader analysis: testId=${document.testId}, score=${document.overallScore}%, questions=${document.questionAnalysis?.length || 0}, gradedAt=${document.gradedAt}`);
          break;

        case 'questions':
          console.log(`[QUERY-EXECUTION] Question: topic=${document.topic}, subtopic=${document.subtopic}, difficulty=${document.difficulty}`);
          break;

        case 'chatConversations':
          console.log(`[QUERY-EXECUTION] Chat: userId=${document.userId}, subject=${document.subject}, messages=${document.messages?.length || 0}, updated=${document.updatedAt}`);
          break;

        default:
          console.log(`[QUERY-EXECUTION] ${collection} document with ${Object.keys(document).length} fields`);
      }
    } catch (error) {
      console.log(`[QUERY-EXECUTION] Error logging fields for ${collection}:`, error.message);
    }
  }

  /**
   * Validate user access to specific collections
   */
  validateAccess(collection, userType) {
    const accessMatrix = this.accessControlMatrix[userType];
    if (!accessMatrix) {
      return false;
    }

    return accessMatrix.includes(collection) || accessMatrix.includes('*');
  }

  /**
   * Apply user-specific filters to queries for security
   */
  async applyUserFilters(query, userType, userId, subject) {
    const secureQuery = { ...query };

    // Ensure query.query exists
    if (!secureQuery.query) {
      secureQuery.query = {};
    }

    // Apply user-specific filters based on collection and user type
    switch (query.collection) {
      case 'students':
        if (userType === 'student') {
          // Students can only access their own data
          secureQuery.query = { ...secureQuery.query, username: userId };
        } else if (userType === 'teacher') {
          // Teachers can access their class students
          const teacherStudentFilter = await this.getTeacherStudentFilter(userId);
          secureQuery.query = { ...secureQuery.query, ...teacherStudentFilter };
        }
        break;

      case 'studentKnowledgeGraph':
        if (userType === 'student') {
          secureQuery.query = { ...secureQuery.query, studentId: userId };
        } else if (userType === 'teacher') {
          // Teachers access class-wide data
          const teacherStudentFilter = await this.getTeacherStudentFilter(userId);
          secureQuery.query = { ...secureQuery.query, ...teacherStudentFilter };
        }
        break;

      case 'testHistory':
        // Add subject filter if not present
        if (subject && !secureQuery.query.subject) {
          secureQuery.query = { ...secureQuery.query, subject };
        }

        if (userType === 'teacher') {
          // Teachers can access test history for their class students
          const teacherStudentFilter = await this.getTeacherStudentFilter(userId);
          // For testHistory, we need to filter by studentId field
          if (teacherStudentFilter.$or) {
            // Extract student IDs from the filter
            const studentIds = teacherStudentFilter.$or
              .find(filter => filter.studentId)?.studentId?.$in || [];
            if (studentIds.length > 0) {
              secureQuery.query = { ...secureQuery.query, studentId: { $in: studentIds } };
            }
          }
        }
        break;

      case 'aegisGrader':
        if (userType === 'student') {
          // Filter by student's admission number
          secureQuery.query = {
            ...secureQuery.query,
            'answerSheets.rollNumber': userId // Assuming userId is admission number
          };
        } else if (userType === 'teacher') {
          // Teachers can access aegisGrader data for their classes
          const teacherClasses = await Class.find({ teacherId: userId }).select('className');
          const classNames = teacherClasses.map(cls => cls.className);
          if (classNames.length > 0) {
            secureQuery.query = {
              ...secureQuery.query,
              'testDetails.className': { $in: classNames }
            };
          }
        }
        if (subject && !secureQuery.query.subject) {
          secureQuery.query = { ...secureQuery.query, subject };
        }
        break;

      case 'chatConversations':
        if (userType === 'teacher') {
          // Block teacher access to chat conversations
          throw new Error('Teachers cannot access student chat conversations');
        }
        secureQuery.query = { ...secureQuery.query, userId };
        break;
    }

    // Apply privacy projection - ensure we don't mix inclusion and exclusion
    const privacyProjection = this.privacyFilters[query.collection] || {};
    const queryProjection = query.projection || {};

    // Check if query projection has inclusion fields (value: 1)
    const hasInclusion = Object.values(queryProjection).some(val => val === 1);
    const hasExclusion = Object.values(queryProjection).some(val => val === 0);

    if (hasInclusion && hasExclusion) {
      // If mixed, prioritize exclusion for safety
      secureQuery.projection = {
        ...Object.fromEntries(Object.entries(queryProjection).filter(([k, v]) => v === 0)),
        ...privacyProjection
      };
    } else {
      // Safe to merge
      secureQuery.projection = {
        ...queryProjection,
        ...privacyProjection
      };
    }

    return secureQuery;
  }

  /**
   * Execute a secure query against the appropriate model
   */
  async executeSecureQuery(query) {
    const model = this.getModel(query.collection, query.subject);
    
    if (!model) {
      throw new Error(`Model not found for collection: ${query.collection}`);
    }

    // Set query timeout
    const options = {
      maxTimeMS: query.optimizationHints?.maxTimeMS || 5000
    };

    if (query.operation === 'aggregate') {
      // Execute aggregation pipeline
      return await model.aggregate(query.pipeline).option(options).exec();
    } else {
      // Execute find query
      let queryBuilder = model.find(query.query, query.projection, options);
      
      if (query.sort) {
        queryBuilder = queryBuilder.sort(query.sort);
      }
      
      if (query.limit) {
        queryBuilder = queryBuilder.limit(Math.min(query.limit, 100)); // Safety limit
      }
      
      return await queryBuilder.exec();
    }
  }

  /**
   * Get the appropriate Mongoose model for a collection
   */
  getModel(collection, subject) {
    switch (collection) {
      case 'students':
        return Student;
      case 'studentKnowledgeGraph':
        return StudentCurriculumModel;
      case 'testHistory':
        return TestHistory;
      case 'aegisGrader':
        return AegisGrader;
      case 'chatConversations':
        return ChatConversation;
      case 'curriculumNodes':
        return subject ? createKnowledgeGraphModel(subject) : null;
      case 'questions':
        // Dynamic question model - would need collection name from query
        return createQuestionModel('questionBank_class10_mathematics'); // Default
      case 'aiQuestions':
        return AIQuestion;
      case 'questionTemp':
        return QuestionTemp;
      default:
        return null;
    }
  }

  /**
   * Sanitize query results to remove sensitive data
   */
  sanitizeResults(results, collection, userType) {
    if (!results || results.length === 0) {
      return results;
    }

    const sanitizedResults = Array.isArray(results) ? results : [results];
    
    return sanitizedResults.map(doc => {
      const sanitized = doc.toObject ? doc.toObject() : doc;
      
      // Remove sensitive fields
      this.removeSensitiveFields(sanitized);
      
      // Resolve ObjectIds to meaningful content
      this.resolveObjectIds(sanitized, collection);
      
      // Apply collection-specific sanitization
      this.applyCollectionSanitization(sanitized, collection, userType);
      
      return sanitized;
    });
  }

  /**
   * Remove sensitive fields from results
   */
  removeSensitiveFields(doc) {
    const sensitiveFields = [
      'password', 'securityToken', 'email', 'metadata.ipAddress',
      'metadata.device', 'metadata.browser', '_id', '__v'
    ];

    sensitiveFields.forEach(field => {
      if (field.includes('.')) {
        // Handle nested fields
        const parts = field.split('.');
        let current = doc;
        for (let i = 0; i < parts.length - 1; i++) {
          if (current[parts[i]]) {
            current = current[parts[i]];
          } else {
            break;
          }
        }
        if (current && current[parts[parts.length - 1]]) {
          delete current[parts[parts.length - 1]];
        }
      } else {
        delete doc[field];
      }
    });
  }

  /**
   * Resolve ObjectIds to meaningful content
   */
  resolveObjectIds(doc, collection) {
    // This would be expanded to resolve specific ObjectIds to meaningful names
    // For now, just convert ObjectIds to strings to avoid exposure
    this.convertObjectIdsToStrings(doc);
  }

  /**
   * Convert ObjectIds to strings recursively
   */
  convertObjectIdsToStrings(obj) {
    if (obj && typeof obj === 'object') {
      Object.keys(obj).forEach(key => {
        if (mongoose.Types.ObjectId.isValid(obj[key])) {
          obj[key] = obj[key].toString();
        } else if (typeof obj[key] === 'object') {
          this.convertObjectIdsToStrings(obj[key]);
        }
      });
    }
  }

  /**
   * Apply collection-specific sanitization rules
   */
  applyCollectionSanitization(doc, collection, userType) {
    switch (collection) {
      case 'students':
        if (userType === 'teacher') {
          // Remove personal details for teachers
          delete doc.admissionNumber;
          delete doc.profileImage;
        }
        break;
      
      case 'testHistory':
        // Remove full question content for performance
        if (doc.questions) {
          doc.questionCount = doc.questions.length;
          delete doc.questions;
        }
        break;
    }
  }

  /**
   * Get teacher's class filter - returns filter to access only teacher's assigned classes
   */
  async getTeacherClassFilter(teacherId) {
    try {
      // Add timeout to prevent hanging database queries
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Teacher class filter query timeout')), 5000);
      });

      const queryPromise = (async () => {
        // Find all classes where this teacher is assigned
        const teacherClasses = await Class.find({ teacherId: teacherId }).select('_id');
        const classIds = teacherClasses.map(cls => cls._id);

        if (classIds.length === 0) {
          console.log(`[QUERY-ENGINE] No classes found for teacher: ${teacherId}`);
          return { _id: { $in: [] } }; // Return empty result set
        }

        console.log(`[QUERY-ENGINE] Teacher ${teacherId} has access to ${classIds.length} classes`);
        return { classId: { $in: classIds } };
      })();

      return await Promise.race([queryPromise, timeoutPromise]);
    } catch (error) {
      console.error('[QUERY-ENGINE] Error getting teacher class filter:', error);
      // Return empty result set on error to prevent system failure
      return { _id: { $in: [] } };
    }
  }

  /**
   * Get teacher's student filter - returns filter to access only students in teacher's classes
   */
  async getTeacherStudentFilter(teacherId) {
    try {
      // Add timeout to prevent hanging database queries
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Teacher student filter query timeout')), 5000);
      });

      const queryPromise = (async () => {
        // Find all classes where this teacher is assigned
        const teacherClasses = await Class.find({ teacherId: teacherId }).populate('students');

        if (teacherClasses.length === 0) {
          console.log(`[QUERY-ENGINE] No classes found for teacher: ${teacherId}`);
          return { _id: { $in: [] } }; // Return empty result set
        }

        // Extract all student IDs from teacher's classes
        const studentIds = [];
        teacherClasses.forEach(cls => {
          if (cls.students && cls.students.length > 0) {
            cls.students.forEach(student => {
              if (student._id) {
                studentIds.push(student._id);
              }
            });
          }
        });

        if (studentIds.length === 0) {
          console.log(`[QUERY-ENGINE] No students found in teacher ${teacherId}'s classes`);
          return { _id: { $in: [] } }; // Return empty result set
        }

        console.log(`[QUERY-ENGINE] Teacher ${teacherId} has access to ${studentIds.length} students across ${teacherClasses.length} classes`);

        // For studentKnowledgeGraph collection, we need to filter by studentId field
        // For students collection, we filter by _id field
        return {
          $or: [
            { _id: { $in: studentIds } },           // For students collection
            { studentId: { $in: studentIds.map(id => id.toString()) } }  // For studentKnowledgeGraph collection
          ]
        };
      })();

      return await Promise.race([queryPromise, timeoutPromise]);
    } catch (error) {
      console.error('[QUERY-ENGINE] Error getting teacher student filter:', error);
      // Return empty result set on error to prevent system failure
      return { _id: { $in: [] } };
    }
  }

  /**
   * Initialize model mapping
   */
  initializeModelMap() {
    return {
      students: Student,
      studentKnowledgeGraph: StudentCurriculumModel,
      testHistory: TestHistory,
      aegisGrader: AegisGrader,
      chatConversations: ChatConversation
    };
  }

  /**
   * Initialize privacy filters for each collection
   */
  initializePrivacyFilters() {
    return {
      students: {
        password: 0,
        securityToken: 0,
        email: 0,
        'subjects.attemptedTests.metadata': 0
      },
      testHistory: {
        questions: 0 // Exclude for performance
      },
      aegisGrader: {
        'answerSheets.metadata': 0
      },
      chatConversations: {
        'messages.metadata': 0
      }
    };
  }

  /**
   * Initialize access control matrix
   */
  initializeAccessControlMatrix() {
    return {
      student: [
        'students', 'studentKnowledgeGraph', 'testHistory', 
        'aegisGrader', 'chatConversations', 'curriculumNodes', 
        'questions', 'aiQuestions'
      ],
      teacher: [
        'students', 'studentKnowledgeGraph', 'testHistory', 
        'aegisGrader', 'curriculumNodes', 'questions', 'aiQuestions'
        // Note: chatConversations is excluded for teachers
      ]
    };
  }
}

export default new QueryExecutionEngine();
